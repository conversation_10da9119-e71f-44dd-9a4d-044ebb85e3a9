{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/socket.service\";\nimport * as i3 from \"../../services/project.service\";\nimport * as i4 from \"../../services/agent.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"./reverse.pipe\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatComponent_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r14.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(model_r14.name);\n  }\n}\nfunction ChatComponent_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(model_r15.name);\n  }\n}\nfunction ChatComponent_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r16.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(model_r16.name);\n  }\n}\nfunction ChatComponent_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const llm_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", llm_r17.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(llm_r17.name);\n  }\n}\nfunction ChatComponent_div_43_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"span\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 56);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 57);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const log_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"log-\" + log_r19.level);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 4, log_r19.timestamp, \"HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(log_r19.level.toUpperCase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r19.message);\n  }\n}\nfunction ChatComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"span\");\n    i0.ɵɵtext(3, \"\\uD83D\\uDD0D Debug Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_43_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.clearDebugLogs());\n    });\n    i0.ɵɵtext(5, \"Clear\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 52);\n    i0.ɵɵtemplate(7, ChatComponent_div_43_div_7_Template, 8, 7, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.debugLogs.slice(-10));\n  }\n}\nfunction ChatComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"p\");\n    i0.ɵɵtext(2, \"No messages yet. Start a conversation with the AI agent.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_div_47_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"U\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"AI\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"S\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83C\\uDF10\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCBB\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u26A0\\uFE0F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE4\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE5\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDC64 You\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDD16 AI Agent\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u2699\\uFE0F System\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDD0D Browser\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 OpenAI\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 LM Studio\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCBB Local LLM\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCBB Terminal\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 OpenAI\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 LM Studio\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u26A0\\uFE0F Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCCB Plan\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE3 Notification\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE4 API Request\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE5 API Response\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(message_r22.metadata.modelId);\n  }\n}\nfunction ChatComponent_div_47_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵelement(1, \"div\", 75)(2, \"div\", 75)(3, \"div\", 75);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Execution time: \", message_r22.metadata.executionTime, \"ms\");\n  }\n}\nfunction ChatComponent_div_47_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9) {\n  return {\n    \"user-message\": a0,\n    \"agent-message\": a1,\n    \"system-message\": a2,\n    \"browser-message\": a3,\n    \"openai-message\": a4,\n    \"llm-message\": a5,\n    \"error-message\": a6,\n    \"api-request-message\": a7,\n    \"api-response-message\": a8,\n    \"streaming\": a9\n  };\n};\nconst _c2 = function (a0, a1, a2, a3, a4, a5, a6, a7, a8) {\n  return {\n    \"user-avatar\": a0,\n    \"ai-avatar\": a1,\n    \"system-avatar\": a2,\n    \"browser-avatar\": a3,\n    \"openai-avatar\": a4,\n    \"llm-avatar\": a5,\n    \"error-avatar\": a6,\n    \"api-request-avatar\": a7,\n    \"api-response-avatar\": a8\n  };\n};\nfunction ChatComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵtemplate(2, ChatComponent_div_47_span_2_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(3, ChatComponent_div_47_span_3_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(4, ChatComponent_div_47_span_4_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(5, ChatComponent_div_47_span_5_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(6, ChatComponent_div_47_span_6_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(7, ChatComponent_div_47_span_7_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(8, ChatComponent_div_47_span_8_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(9, ChatComponent_div_47_span_9_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(10, ChatComponent_div_47_span_10_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(11, ChatComponent_div_47_span_11_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 62)(13, \"div\", 63)(14, \"span\", 64);\n    i0.ɵɵtemplate(15, ChatComponent_div_47_span_15_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(16, ChatComponent_div_47_span_16_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(17, ChatComponent_div_47_span_17_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(18, ChatComponent_div_47_span_18_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(19, ChatComponent_div_47_span_19_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(20, ChatComponent_div_47_span_20_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(21, ChatComponent_div_47_span_21_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(22, ChatComponent_div_47_span_22_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(23, ChatComponent_div_47_span_23_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(24, ChatComponent_div_47_span_24_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(25, ChatComponent_div_47_span_25_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(26, ChatComponent_div_47_span_26_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(27, ChatComponent_div_47_span_27_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(28, ChatComponent_div_47_span_28_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(29, ChatComponent_div_47_span_29_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 65);\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, ChatComponent_div_47_span_33_Template, 2, 1, \"span\", 66);\n    i0.ɵɵtemplate(34, ChatComponent_div_47_span_34_Template, 4, 0, \"span\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(35, \"div\", 68);\n    i0.ɵɵtemplate(36, ChatComponent_div_47_div_36_Template, 3, 1, \"div\", 69);\n    i0.ɵɵelementStart(37, \"div\", 70)(38, \"div\", 71)(39, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_47_Template_button_click_39_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r57);\n      const message_r22 = restoredCtx.$implicit;\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.addReaction(message_r22.id, \"like\"));\n    });\n    i0.ɵɵtext(40, \" \\uD83D\\uDC4D \");\n    i0.ɵɵtemplate(41, ChatComponent_div_47_span_41_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_47_Template_button_click_42_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r57);\n      const message_r22 = restoredCtx.$implicit;\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.addReaction(message_r22.id, \"dislike\"));\n    });\n    i0.ɵɵtext(43, \" \\uD83D\\uDC4E \");\n    i0.ɵɵtemplate(44, ChatComponent_div_47_span_44_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_47_Template_button_click_45_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r57);\n      const message_r22 = restoredCtx.$implicit;\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.addReaction(message_r22.id, \"love\"));\n    });\n    i0.ɵɵtext(46, \" \\u2764\\uFE0F \");\n    i0.ɵɵtemplate(47, ChatComponent_div_47_span_47_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const message_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunctionV(44, _c1, [message_r22.sender === \"user\", message_r22.sender === \"agent\", message_r22.sender === \"system\", message_r22.messageType === \"browser\", message_r22.messageType === \"openai\", message_r22.messageType === \"local_llm\", message_r22.messageType === \"error\", message_r22.messageType === \"api_request\", message_r22.messageType === \"api_response\", message_r22.sender === \"agent\" && !message_r22.isComplete]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunctionV(55, _c2, [message_r22.sender === \"user\", message_r22.sender === \"agent\", message_r22.sender === \"system\", message_r22.messageType === \"browser\", message_r22.messageType === \"openai\", message_r22.messageType === \"local_llm\", message_r22.messageType === \"error\", message_r22.messageType === \"api_request\", message_r22.messageType === \"api_response\"]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"agent\" && !message_r22.messageType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"system\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"browser\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"local_llm\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"error\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"system_notification\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"api_request\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"api_response\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"agent\" && !message_r22.messageType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"system\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"browser\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"lm_studio\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"local_llm\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"terminal\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"llm_openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"llm_lm_studio\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"error\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"plan\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"system_notification\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"api_request\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"api_response\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(32, 41, message_r22.timestamp, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r22.metadata == null ? null : message_r22.metadata.modelId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"agent\" && !message_r22.isComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", message_r22.content, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.metadata && message_r22.metadata.executionTime);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", message_r22.reactions && message_r22.reactions.includes(\"like\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r22.reactions && message_r22.reactions.includes(\"like\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", message_r22.reactions && message_r22.reactions.includes(\"dislike\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r22.reactions && message_r22.reactions.includes(\"dislike\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", message_r22.reactions && message_r22.reactions.includes(\"love\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r22.reactions && message_r22.reactions.includes(\"love\"));\n  }\n}\nfunction ChatComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵelement(1, \"div\", 75)(2, \"div\", 75)(3, \"div\", 75);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵelement(1, \"div\", 80);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"AI Agent is thinking...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82)(2, \"h4\");\n    i0.ɵɵtext(3, \"Agent Thinking Process\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_51_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.toggleAgentThinking());\n    });\n    i0.ɵɵtext(5, \"Close\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\", 84);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r10.agentThinkingContent);\n  }\n}\nfunction ChatComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"span\", 80);\n    i0.ɵɵelementStart(2, \"span\", 86);\n    i0.ɵɵtext(3, \"The AI assistant is working on a complex, long-running task. This may take several minutes. Please do not close this window.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵtext(1, \"GPT\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115);\n    i0.ɵɵtext(1, \"Local LLM\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵtext(1, \"HOT\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtext(1, \"Web Search Results\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118);\n    i0.ɵɵtext(1, \"File Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"span\");\n    i0.ɵɵtext(2, \"Was this helpful?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 120)(4, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_53_div_31_div_21_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const i_r64 = i0.ɵɵnextContext().index;\n      const ctx_r72 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r72.setSubtaskFeedback(i_r64, \"up\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_53_div_31_div_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const i_r64 = i0.ɵɵnextContext().index;\n      const ctx_r75 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r75.setSubtaskFeedback(i_r64, \"down\"));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const subtask_r63 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"selected\", subtask_r63.feedback === \"up\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"selected\", subtask_r63.feedback === \"down\");\n  }\n}\nfunction ChatComponent_div_53_div_31_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r80 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_53_div_31_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r80);\n      const i_r64 = i0.ɵɵnextContext().index;\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.retrySubtask(i_r64));\n    });\n    i0.ɵɵtext(1, \" Retry \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98)(1, \"div\", 99)(2, \"div\", 100);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 101);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ChatComponent_div_53_div_31_div_6_Template, 2, 0, \"div\", 102);\n    i0.ɵɵtemplate(7, ChatComponent_div_53_div_31_div_7_Template, 2, 0, \"div\", 103);\n    i0.ɵɵtemplate(8, ChatComponent_div_53_div_31_div_8_Template, 2, 0, \"div\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 105)(10, \"div\", 106);\n    i0.ɵɵtext(11, \"\\u25B6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 107)(15, \"div\", 108);\n    i0.ɵɵtext(16, \"Output:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 109);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ChatComponent_div_53_div_31_div_19_Template, 2, 0, \"div\", 110);\n    i0.ɵɵtemplate(20, ChatComponent_div_53_div_31_div_20_Template, 2, 0, \"div\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, ChatComponent_div_53_div_31_div_21_Template, 6, 4, \"div\", 112);\n    i0.ɵɵtemplate(22, ChatComponent_div_53_div_31_button_22_Template, 2, 0, \"button\", 113);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subtask_r63 = ctx.$implicit;\n    const i_r64 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r64 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(subtask_r63.subtask.type || \"COMMAND\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.model_type === \"openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.model_type === \"local\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.web_research_used);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(subtask_r63.subtask.description || \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"error\", subtask_r63.error);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", subtask_r63.result || \"Processing...\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.web_results);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.file_diff);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.error);\n  }\n}\nfunction ChatComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"h4\");\n    i0.ɵɵtext(3, \"Autonomous Agent Workflow\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 89)(5, \"div\", 90)(6, \"span\");\n    i0.ɵɵtext(7, \"\\uD83D\\uDD0D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\");\n    i0.ɵɵtext(9, \"Planning\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"div\", 91);\n    i0.ɵɵelementStart(11, \"div\", 92)(12, \"span\");\n    i0.ɵɵtext(13, \"\\uD83C\\uDFA8\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\");\n    i0.ɵɵtext(15, \"Design\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(16, \"div\", 91);\n    i0.ɵɵelementStart(17, \"div\", 93)(18, \"span\");\n    i0.ɵɵtext(19, \"\\uD83D\\uDEE0\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\");\n    i0.ɵɵtext(21, \"Implementation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(22, \"div\", 91);\n    i0.ɵɵelementStart(23, \"div\", 94)(24, \"span\");\n    i0.ɵɵtext(25, \"\\uD83E\\uDDEA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\");\n    i0.ɵɵtext(27, \"Testing\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 95);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 96);\n    i0.ɵɵtemplate(31, ChatComponent_div_53_div_31_Template, 23, 13, \"div\", 97);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 4);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r12.completedSubtasks, \"/\", ctx_r12.subtasks.length, \" steps completed \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.subtasks);\n  }\n}\nfunction ChatComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵtext(1, \"Saving messages...\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    \"expanded\": a0\n  };\n};\nconst _c4 = function (a0) {\n  return {\n    \"active\": a0\n  };\n};\nconst _c5 = function (a0, a1) {\n  return {\n    \"fa-chevron-up\": a0,\n    \"fa-chevron-down\": a1\n  };\n};\nexport class ChatComponent {\n  constructor(fb, socketService, projectService, agentService) {\n    this.fb = fb;\n    this.socketService = socketService;\n    this.projectService = projectService;\n    this.agentService = agentService;\n    this.projectName = '';\n    this.messagesLoading = false;\n    this.messagesSaving = false;\n    this.messageEvent = new EventEmitter();\n    this.chatExpandChange = new EventEmitter();\n    this.messages = [];\n    this.loading = false;\n    this.models = [];\n    this.selectedModel = 'deepseek/deepseek-coder';\n    this.localLlmModels = [{\n      id: 'mistral-nemo-instruct-2407',\n      name: 'Mistral Nemo Instruct 2407'\n    }\n    // Add more local LLM models here if needed\n    ];\n\n    this.selectedLocalLlmModel = 'mistral-nemo-instruct-2407';\n    this.isChatExpanded = false;\n    this.subtasks = [];\n    this.autonomousMode = false;\n    this.agentTyping = false;\n    this.agentThinkingContent = '';\n    this.showAgentThinking = false;\n    this.streamingEnabled = true;\n    this.showApiPayloads = true;\n    this.apiRequests = [];\n    this.apiResponses = [];\n    this.debugLogs = [];\n  }\n  ngOnInit() {\n    console.log('[ChatComponent] ngOnInit called');\n    this.initForm();\n    this.loadMessages();\n    this.loadModels();\n    this.setupSocketListeners();\n  }\n  ngAfterViewChecked() {\n    this.scrollToBottom();\n  }\n  scrollToBottom() {\n    try {\n      if (this.messagesContainer && this.messagesContainer.nativeElement) {\n        this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\n      }\n    } catch (err) {}\n  }\n  initForm() {\n    console.log('[ChatComponent] Initializing form');\n    this.messageForm = this.fb.group({\n      message: ['', Validators.required]\n    });\n  }\n  loadMessages() {\n    console.log('[ChatComponent] loadMessages called for project:', this.projectName);\n    if (!this.projectName) {\n      console.warn('[ChatComponent] No projectName found. Skipping loadMessages.');\n      return;\n    }\n    if (!this.messagesLoading) {\n      this.loading = true;\n    }\n    this.projectService.getProjectMessages(this.projectName).subscribe(response => {\n      console.log('[ChatComponent] Project messages loaded:', response);\n      this.messages = response.messages || [];\n      this.loading = false;\n    }, error => {\n      console.error('[ChatComponent] ❌ Error loading messages:', error);\n      this.loading = false;\n    });\n  }\n  loadModels() {\n    console.log('[ChatComponent] loadModels called');\n    this.agentService.getModels().subscribe(response => {\n      console.log('[ChatComponent] Models loaded:', response);\n      this.models = response.models || [];\n    }, error => {\n      console.error('[ChatComponent] ❌ Error loading models:', error);\n    });\n  }\n  /**\n   * Gets models filtered by provider\n   */\n  getModelsByProvider(provider) {\n    return this.models.filter(model => model.id.startsWith(`${provider}/`));\n  }\n  /**\n   * Gets models that don't belong to specified providers\n   */\n  getOtherModels() {\n    const knownProviders = ['openai', 'deepseek'];\n    return this.models.filter(model => !knownProviders.some(provider => model.id.startsWith(`${provider}/`)));\n  }\n  setupSocketListeners() {\n    console.log('[ChatComponent] Setting up socket listeners');\n    this.socketService.on('agent_message').subscribe(data => {\n      console.log('[ChatComponent] 🔁 Received socket \"agent_message\":', data);\n      this.addDebugLog('info', `Agent message received: ${data.message?.substring(0, 50)}...`);\n      if (data.project_name === this.projectName) {\n        this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\n        // Determine the message type from incoming data\n        const messageType = data.message_type || 'agent';\n        this.messages.push({\n          id: data.message_id || `msg-${Date.now()}`,\n          sender: 'agent',\n          content: data.message,\n          timestamp: new Date(),\n          isAgentWorkingPlaceholder: false,\n          messageType: messageType,\n          reactions: []\n        });\n        this.loading = false;\n        console.log('[ChatComponent] Message added to chat from agent');\n      }\n    });\n    this.socketService.on('agent_typing').subscribe(data => {\n      console.log('[ChatComponent] 🔁 Received socket \"agent_typing\":', data);\n      if (data.project_name === this.projectName) {\n        this.agentTyping = data.is_typing;\n        // If not typing anymore, remove any placeholder messages\n        if (!data.is_typing) {\n          this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\n        }\n      }\n    });\n    this.socketService.on('agent_stream_token').subscribe(data => {\n      console.log('[ChatComponent] 🔁 Received socket \"agent_stream_token\":', data);\n      if (data.project_name === this.projectName) {\n        // Find the last agent message or create a new one if none exists\n        let lastAgentMessage = this.messages.find(m => m.sender === 'agent' && !m.isComplete);\n        if (!lastAgentMessage) {\n          lastAgentMessage = {\n            id: `stream-${Date.now()}`,\n            sender: 'agent',\n            content: '',\n            timestamp: new Date(),\n            isComplete: false,\n            reactions: []\n          };\n          this.messages.push(lastAgentMessage);\n        }\n        // Append the token to the message content\n        lastAgentMessage.content += data.token;\n        this.scrollToBottom();\n      }\n    });\n    this.socketService.on('agent_complete').subscribe(data => {\n      console.log('[ChatComponent] ✅ Received socket \"agent_complete\":', data);\n      if (data.project_name === this.projectName) {\n        this.loading = false;\n        this.agentTyping = false;\n        // Mark all agent messages as complete\n        this.messages.forEach(message => {\n          if (message.sender === 'agent') {\n            message.isComplete = true;\n          }\n        });\n        // Remove any placeholder messages\n        this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\n        console.log('[ChatComponent] Loading state cleared after agent_complete');\n      }\n    });\n    this.socketService.on('agent_thinking').subscribe(data => {\n      console.log('[ChatComponent] 🧠 Received socket \"agent_thinking\":', data);\n      if (data.project_name === this.projectName && this.showAgentThinking) {\n        // Display the agent's thought process in a special UI element if debugging is enabled\n        this.agentThinkingContent = data.thinking;\n      }\n    });\n    this.socketService.on('message_reaction').subscribe(data => {\n      console.log('[ChatComponent] 👍 Received socket \"message_reaction\":', data);\n      if (data.project_name === this.projectName) {\n        // Find the message and add the reaction\n        const messageToUpdate = this.messages.find(m => m.id === data.message_id);\n        if (messageToUpdate) {\n          if (!messageToUpdate.reactions) {\n            messageToUpdate.reactions = [];\n          }\n          if (!messageToUpdate.reactions.includes(data.reaction)) {\n            messageToUpdate.reactions.push(data.reaction);\n          }\n        }\n      }\n    });\n    // Listen for any error events\n    this.socketService.on('error').subscribe(data => {\n      console.error('[ChatComponent] ❌ Socket error:', data);\n      this.addDebugLog('error', `Socket error: ${JSON.stringify(data)}`);\n    });\n    // Listen for agent errors specifically\n    this.socketService.on('agent_error').subscribe(data => {\n      console.error('[ChatComponent] ❌ Agent error:', data);\n      this.addDebugLog('error', `Agent error: ${data.error || 'Unknown error'}`);\n      if (data.project_name === this.projectName) {\n        this.loading = false;\n        this.agentTyping = false;\n        // Add error message to chat\n        this.messages.push({\n          id: `error-${Date.now()}`,\n          sender: 'system',\n          messageType: 'error',\n          content: `<strong>Agent Error:</strong><br>${data.error || 'Unknown error occurred'}`,\n          timestamp: new Date(),\n          reactions: []\n        });\n      }\n    });\n  }\n  sendMessage() {\n    if (this.messageForm.invalid) {\n      return;\n    }\n    const messageContent = this.messageForm.get('message')?.value;\n    if (!messageContent || !this.projectName) {\n      return;\n    }\n    // Add user message to the chat\n    const userMessageId = `msg-${Date.now()}`;\n    this.messages.push({\n      id: userMessageId,\n      sender: 'user',\n      content: messageContent,\n      timestamp: new Date(),\n      reactions: []\n    });\n    // Create request payload\n    const requestPayload = {\n      project_name: this.projectName,\n      message: messageContent,\n      model_id: this.selectedModel,\n      local_llm_model_id: this.selectedLocalLlmModel,\n      streaming_enabled: this.streamingEnabled\n    };\n    // Store request\n    const requestEntry = {\n      timestamp: new Date(),\n      type: 'request',\n      endpoint: `/projects/${this.projectName}/messages`,\n      payload: requestPayload\n    };\n    this.apiRequests.push(requestEntry);\n    // If API payloads are visible, add to messages\n    if (this.showApiPayloads) {\n      this.messages.push({\n        id: `api-req-${Date.now()}`,\n        sender: 'system',\n        messageType: 'api_request',\n        content: `<strong>API Request:</strong><br><pre>${JSON.stringify(requestPayload, null, 2)}</pre>`,\n        timestamp: new Date(),\n        reactions: []\n      });\n    }\n    // Reset the form\n    this.messageForm.reset();\n    // Show loading indicator\n    this.loading = true;\n    // Send to API\n    this.addDebugLog('info', `Sending message with model: ${this.selectedModel}`);\n    if (this.streamingEnabled) {\n      // For streaming, we handle via sockets\n      this.addDebugLog('info', 'Using streaming mode via WebSocket');\n      this.socketService.sendMessage(this.projectName, messageContent, this.selectedModel);\n    } else {\n      // For non-streaming, we make a direct API call\n      this.agentService.sendMessage(this.projectName, messageContent, this.selectedModel, this.selectedLocalLlmModel, false).subscribe(response => {\n        // Store response\n        const responseEntry = {\n          timestamp: new Date(),\n          type: 'response',\n          endpoint: `/projects/${this.projectName}/messages`,\n          payload: response\n        };\n        this.apiResponses.push(responseEntry);\n        // If API payloads are visible, add to messages\n        if (this.showApiPayloads) {\n          this.messages.push({\n            id: `api-res-${Date.now()}`,\n            sender: 'system',\n            messageType: 'api_response',\n            content: `<strong>API Response:</strong><br><pre>${JSON.stringify(response, null, 2)}</pre>`,\n            timestamp: new Date(),\n            reactions: []\n          });\n        }\n        this.loading = false;\n      }, error => {\n        console.error('[ChatComponent] ❌ Error sending message:', error);\n        // Store error response\n        const errorEntry = {\n          timestamp: new Date(),\n          type: 'error',\n          endpoint: `/projects/${this.projectName}/messages`,\n          payload: error\n        };\n        this.apiResponses.push(errorEntry);\n        // Add error message\n        this.messages.push({\n          id: `error-${Date.now()}`,\n          sender: 'system',\n          messageType: 'error',\n          content: `<strong>API Error:</strong><br><pre>${JSON.stringify(error, null, 2)}</pre>`,\n          timestamp: new Date(),\n          reactions: []\n        });\n        this.loading = false;\n      });\n    }\n  }\n  onModelChange(modelId) {\n    console.log('[ChatComponent] Model changed to:', modelId);\n    this.selectedModel = modelId;\n  }\n  deleteMessage(index) {\n    this.messages.splice(index, 1);\n  }\n  clearChat() {\n    if (!this.projectName) return;\n    // Clear messages in UI\n    this.messages = [];\n    // Call backend API to delete chat history\n    this.projectService.deleteProjectMessages(this.projectName).subscribe(() => {\n      console.log('[ChatComponent] Chat history deleted on backend');\n    }, error => {\n      console.error('[ChatComponent] Error deleting chat history:', error);\n    });\n  }\n  toggleChatExpand() {\n    this.isChatExpanded = !this.isChatExpanded;\n    this.chatExpandChange.emit(this.isChatExpanded);\n  }\n  onEnter(event) {\n    if (event.shiftKey) {\n      return; // allow newline\n    }\n\n    event.preventDefault();\n    this.sendMessage();\n  }\n  exportChat() {\n    if (!this.projectName) return;\n    this.projectService.exportProjectChat(this.projectName).subscribe(response => {\n      alert('Chat exported: ' + (response?.file_path || 'Success'));\n    }, error => {\n      alert('Failed to export chat: ' + (error?.error?.detail || error));\n    });\n  }\n  exportDynamicChat() {\n    if (!this.messages.length) return;\n    // Format chat as text\n    const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\\n');\n    const blob = new Blob([chatText], {\n      type: 'text/plain'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n    const filename = `${this.projectName || 'chat'}-dynamic-${timestamp}.txt`;\n    a.href = url;\n    a.download = filename;\n    document.body.appendChild(a);\n    a.click();\n    setTimeout(() => {\n      document.body.removeChild(a);\n      window.URL.revokeObjectURL(url);\n    }, 0);\n  }\n  copyChat() {\n    const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\\n');\n    navigator.clipboard.writeText(chatText).then(() => alert('Chat copied to clipboard!'), () => alert('Failed to copy chat to clipboard.'));\n  }\n  onLocalLlmModelChange(modelId) {\n    this.selectedLocalLlmModel = modelId;\n    // You can add logic here to notify the backend or update the session if needed\n    console.log('[ChatComponent] Local LLM model changed to:', modelId);\n  }\n  get completedSubtasks() {\n    if (!this.subtasks) return 0;\n    return this.subtasks.filter(subtask => !subtask.error).length;\n  }\n  retrySubtask(index) {\n    // Simulate retry: clear error and result, set to loading, then re-run (in real app, call backend)\n    const subtask = this.subtasks[index];\n    subtask.error = null;\n    subtask.result = 'Retrying...';\n    // Simulate async retry (replace with real backend call)\n    setTimeout(() => {\n      subtask.result = 'Retried result (simulated)';\n      subtask.error = null;\n    }, 1500);\n  }\n  setSubtaskFeedback(index, feedback) {\n    this.subtasks[index].feedback = feedback;\n  }\n  get longTaskInProgress() {\n    if (this.loading) return true;\n    if (this.subtasks && this.subtasks.length > 0) {\n      return this.subtasks.some(s => !s.result && !s.error);\n    }\n    return false;\n  }\n  toggleAutonomousMode() {\n    this.autonomousMode = !this.autonomousMode;\n  }\n  /**\n   * Formats a unified diff string as HTML with basic syntax highlighting for added, removed, and context lines.\n   * @param diff The unified diff string\n   * @returns HTML string\n   */\n  formatFileDiff(diff) {\n    if (!diff) return '';\n    // Escape HTML\n    const escape = s => s.replace(/[&<>]/g, c => ({\n      '&': '&amp;',\n      '<': '&lt;',\n      '>': '&gt;'\n    })[c] || c);\n    return '<pre>' + diff.split('\\n').map(line => {\n      if (line.startsWith('+') && !line.startsWith('+++')) {\n        return `<span class='diff-added'>${escape(line)}</span>`;\n      } else if (line.startsWith('-') && !line.startsWith('---')) {\n        return `<span class='diff-removed'>${escape(line)}</span>`;\n      } else if (line.startsWith('@@')) {\n        return `<span class='diff-hunk'>${escape(line)}</span>`;\n      } else {\n        return escape(line);\n      }\n    }).join('\\n') + '</pre>';\n  }\n  /**\n   * Returns the current workflow stage for the progress indicator in the chat UI.\n   * 1 = Planning, 2 = Design, 3 = Implementation, 4 = Testing\n   */\n  get currentStage() {\n    if (!this.subtasks || this.subtasks.length === 0) return 0;\n    // If all subtasks are completed, return 4 (Testing)\n    if (this.subtasks.every(s => s.completed || s.result || s.error)) return 4;\n    // Otherwise, estimate stage based on subtask type or index\n    // (You can refine this logic as needed)\n    let stage = 1;\n    for (const subtask of this.subtasks) {\n      if (subtask.subtask?.type === 'design') stage = Math.max(stage, 2);else if (subtask.subtask?.type === 'implementation') stage = Math.max(stage, 3);else if (subtask.subtask?.type === 'testing') stage = Math.max(stage, 4);\n    }\n    return stage;\n  }\n  addReaction(messageId, reaction) {\n    console.log(`[ChatComponent] Adding reaction ${reaction} to message ${messageId}`);\n    // Optimistically update UI\n    const messageToUpdate = this.messages.find(m => m.id === messageId);\n    if (messageToUpdate) {\n      if (!messageToUpdate.reactions) {\n        messageToUpdate.reactions = [];\n      }\n      if (!messageToUpdate.reactions.includes(reaction)) {\n        messageToUpdate.reactions.push(reaction);\n      }\n    }\n    // Send to backend\n    this.agentService.addMessageReaction(this.projectName, messageId, reaction).subscribe(response => {\n      console.log('[ChatComponent] ✅ Reaction added successfully:', response);\n    }, error => {\n      console.error('[ChatComponent] ❌ Error adding reaction:', error);\n      // Remove the reaction if it failed\n      if (messageToUpdate && messageToUpdate.reactions) {\n        messageToUpdate.reactions = messageToUpdate.reactions.filter(r => r !== reaction);\n      }\n    });\n  }\n  clearDebugLogs() {\n    this.debugLogs = [];\n  }\n  addDebugLog(level, message) {\n    this.debugLogs.push({\n      timestamp: new Date(),\n      level: level,\n      message: message\n    });\n    // Keep only the last 50 logs to prevent memory issues\n    if (this.debugLogs.length > 50) {\n      this.debugLogs = this.debugLogs.slice(-50);\n    }\n  }\n  resetContextMemory() {\n    if (!this.projectName) return;\n    this.projectService.resetContextMemory(this.projectName).subscribe(() => {\n      this.addDebugLog('info', 'Context memory reset successfully');\n      console.log('[ChatComponent] Context memory reset');\n    }, error => {\n      this.addDebugLog('error', `Failed to reset context memory: ${error.message}`);\n      console.error('[ChatComponent] Error resetting context memory:', error);\n    });\n  }\n  toggleApiPayloads() {\n    this.showApiPayloads = !this.showApiPayloads;\n    this.addDebugLog('info', `API payloads visibility: ${this.showApiPayloads ? 'enabled' : 'disabled'}`);\n  }\n  toggleAgentThinking() {\n    this.showAgentThinking = !this.showAgentThinking;\n    this.addDebugLog('info', `Agent thinking display: ${this.showAgentThinking ? 'enabled' : 'disabled'}`);\n  }\n  static {\n    this.ɵfac = function ChatComponent_Factory(t) {\n      return new (t || ChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SocketService), i0.ɵɵdirectiveInject(i3.ProjectService), i0.ɵɵdirectiveInject(i4.AgentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatComponent,\n      selectors: [[\"app-chat\"]],\n      viewQuery: function ChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n        }\n      },\n      inputs: {\n        projectName: \"projectName\",\n        messagesLoading: \"messagesLoading\",\n        messagesSaving: \"messagesSaving\"\n      },\n      outputs: {\n        messageEvent: \"messageEvent\",\n        chatExpandChange: \"chatExpandChange\"\n      },\n      decls: 66,\n      vars: 43,\n      consts: [[1, \"chat-container\", 3, \"ngClass\"], [1, \"chat-header\"], [1, \"agent-title\"], [1, \"agent-icon\"], [1, \"agent-status\", 3, \"ngClass\"], [1, \"model-controls\"], [1, \"model-selector\", \"cloud-model\"], [\"for\", \"modelSelect\"], [1, \"fa\", \"fa-cloud\"], [\"id\", \"modelSelect\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"label\", \"OpenAI\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"DeepSeek\"], [1, \"model-selector\", \"local-model\"], [\"for\", \"localLlmSelect\"], [1, \"fa\", \"fa-desktop\"], [\"id\", \"localLlmSelect\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"streaming-toggle\"], [\"for\", \"streamingToggle\"], [\"type\", \"checkbox\", \"id\", \"streamingToggle\", 3, \"ngModel\", \"ngModelChange\"], [1, \"chat-actions\"], [\"title\", \"Toggle between autonomous and assisted mode\", 1, \"mode-toggle-btn\", 3, \"ngClass\", \"click\"], [1, \"fa\", \"fa-robot\"], [\"title\", \"Reset conversation memory\", 1, \"memory-btn\", 3, \"click\"], [1, \"fa\", \"fa-brain\"], [\"title\", \"Toggle API payloads visibility\", 1, \"api-toggle-btn\", 3, \"ngClass\", \"click\"], [1, \"fa\", \"fa-exchange-alt\"], [\"title\", \"Clear chat history\", 1, \"clear-chat-btn\", 3, \"click\"], [1, \"fa\", \"fa-trash\"], [1, \"expand-chat-btn\", 3, \"click\"], [1, \"fa\", 3, \"ngClass\"], [\"class\", \"debug-logs\", 4, \"ngIf\"], [1, \"messages-container\"], [\"messagesContainer\", \"\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"typing-indicator\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"agent-thinking-panel\", 4, \"ngIf\"], [\"class\", \"long-task-banner\", 4, \"ngIf\"], [\"class\", \"autonomous-workflow\", 4, \"ngIf\"], [1, \"message-form\", 3, \"formGroup\", \"ngSubmit\"], [\"formControlName\", \"message\", \"placeholder\", \"Type your message here...\", \"rows\", \"3\", 3, \"disabled\", \"keydown.enter\"], [\"type\", \"submit\", 3, \"disabled\"], [\"type\", \"button\", \"title\", \"Export chat history\", 1, \"export-chat-btn\", 3, \"disabled\", \"click\"], [\"type\", \"button\", \"title\", \"Export chat as file (dynamic, no storage)\", 1, \"export-chat-btn\", 3, \"disabled\", \"click\"], [\"type\", \"button\", \"title\", \"Copy entire chat to clipboard\", 1, \"copy-chat-btn\", 3, \"disabled\", \"click\"], [\"class\", \"saving-indicator\", 4, \"ngIf\"], [3, \"value\"], [1, \"debug-logs\"], [1, \"debug-header\"], [1, \"clear-logs-btn\", 3, \"click\"], [1, \"debug-content\"], [\"class\", \"debug-log-entry\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"debug-log-entry\", 3, \"ngClass\"], [1, \"log-time\"], [1, \"log-level\"], [1, \"log-message\"], [1, \"empty-state\"], [1, \"message\", 3, \"ngClass\"], [1, \"avatar\", 3, \"ngClass\"], [4, \"ngIf\"], [1, \"bubble\"], [1, \"message-header\"], [1, \"sender\"], [1, \"timestamp\"], [\"class\", \"message-type\", 4, \"ngIf\"], [\"class\", \"streaming-indicator\", 4, \"ngIf\"], [1, \"message-content\", 3, \"innerHTML\"], [\"class\", \"message-metadata\", 4, \"ngIf\"], [1, \"message-reactions\"], [1, \"reaction-buttons\"], [1, \"reaction-button\", 3, \"click\"], [1, \"message-type\"], [1, \"streaming-indicator\"], [1, \"typing-dot\"], [1, \"message-metadata\"], [1, \"execution-time\"], [1, \"typing-indicator\"], [1, \"loading-indicator\"], [1, \"spinner\"], [1, \"agent-thinking-panel\"], [1, \"thinking-header\"], [3, \"click\"], [1, \"thinking-content\"], [1, \"long-task-banner\"], [1, \"banner-text\"], [1, \"autonomous-workflow\"], [1, \"workflow-header\"], [1, \"progress-stages\"], [1, \"stage-icon\", \"planning\"], [1, \"stage-connector\"], [1, \"stage-icon\", \"design\"], [1, \"stage-icon\", \"implementation\"], [1, \"stage-icon\", \"testing\"], [1, \"progress-counter\"], [1, \"subtasks-grid\"], [\"class\", \"subtask-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"subtask-card\"], [1, \"subtask-header\"], [1, \"subtask-number\"], [1, \"subtask-type\"], [\"class\", \"model-label\", 4, \"ngIf\"], [\"class\", \"model-label local\", 4, \"ngIf\"], [\"class\", \"hot-label\", 4, \"ngIf\"], [1, \"task-description\"], [1, \"bullet\"], [1, \"output-area\"], [1, \"output-label\"], [1, \"output-content\"], [\"class\", \"web-results\", 4, \"ngIf\"], [\"class\", \"file-changes\", 4, \"ngIf\"], [\"class\", \"feedback-row\", 4, \"ngIf\"], [\"class\", \"retry-button\", 3, \"click\", 4, \"ngIf\"], [1, \"model-label\"], [1, \"model-label\", \"local\"], [1, \"hot-label\"], [1, \"web-results\"], [1, \"file-changes\"], [1, \"feedback-row\"], [1, \"feedback-options\"], [1, \"retry-button\", 3, \"click\"], [1, \"saving-indicator\"]],\n      template: function ChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n          i0.ɵɵtext(4, \"\\uD83E\\uDD16\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"h2\");\n          i0.ɵɵtext(6, \"Autonomous AI Agent\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 4);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 5)(10, \"div\", 6)(11, \"label\", 7);\n          i0.ɵɵelement(12, \"i\", 8);\n          i0.ɵɵtext(13, \" Cloud LLM:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"select\", 9);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_Template_select_ngModelChange_14_listener($event) {\n            return ctx.selectedModel = $event;\n          })(\"change\", function ChatComponent_Template_select_change_14_listener() {\n            return ctx.onModelChange(ctx.selectedModel);\n          });\n          i0.ɵɵelementStart(15, \"optgroup\", 10);\n          i0.ɵɵtemplate(16, ChatComponent_option_16_Template, 2, 2, \"option\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"optgroup\", 12);\n          i0.ɵɵtemplate(18, ChatComponent_option_18_Template, 2, 2, \"option\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, ChatComponent_option_19_Template, 2, 2, \"option\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 13)(21, \"label\", 14);\n          i0.ɵɵelement(22, \"i\", 15);\n          i0.ɵɵtext(23, \" Local LLM:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"select\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_Template_select_ngModelChange_24_listener($event) {\n            return ctx.selectedLocalLlmModel = $event;\n          })(\"change\", function ChatComponent_Template_select_change_24_listener() {\n            return ctx.onLocalLlmModelChange(ctx.selectedLocalLlmModel);\n          });\n          i0.ɵɵtemplate(25, ChatComponent_option_25_Template, 2, 2, \"option\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 17)(27, \"label\", 18)(28, \"input\", 19);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_Template_input_ngModelChange_28_listener($event) {\n            return ctx.streamingEnabled = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(29, \" Streaming \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 20)(31, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_31_listener() {\n            return ctx.toggleAutonomousMode();\n          });\n          i0.ɵɵelement(32, \"i\", 22);\n          i0.ɵɵelementStart(33, \"span\");\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_35_listener() {\n            return ctx.resetContextMemory();\n          });\n          i0.ɵɵelement(36, \"i\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_37_listener() {\n            return ctx.toggleApiPayloads();\n          });\n          i0.ɵɵelement(38, \"i\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_39_listener() {\n            return ctx.clearChat();\n          });\n          i0.ɵɵelement(40, \"i\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_41_listener() {\n            return ctx.toggleChatExpand();\n          });\n          i0.ɵɵelement(42, \"i\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(43, ChatComponent_div_43_Template, 8, 1, \"div\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 32, 33);\n          i0.ɵɵtemplate(46, ChatComponent_div_46_Template, 3, 0, \"div\", 34);\n          i0.ɵɵtemplate(47, ChatComponent_div_47_Template, 48, 65, \"div\", 35);\n          i0.ɵɵpipe(48, \"reverse\");\n          i0.ɵɵtemplate(49, ChatComponent_div_49_Template, 4, 0, \"div\", 36);\n          i0.ɵɵtemplate(50, ChatComponent_div_50_Template, 4, 0, \"div\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(51, ChatComponent_div_51_Template, 8, 1, \"div\", 38);\n          i0.ɵɵtemplate(52, ChatComponent_div_52_Template, 4, 0, \"div\", 39);\n          i0.ɵɵtemplate(53, ChatComponent_div_53_Template, 32, 17, \"div\", 40);\n          i0.ɵɵelementStart(54, \"form\", 41);\n          i0.ɵɵlistener(\"ngSubmit\", function ChatComponent_Template_form_ngSubmit_54_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(55, \"textarea\", 42);\n          i0.ɵɵlistener(\"keydown.enter\", function ChatComponent_Template_textarea_keydown_enter_55_listener($event) {\n            return ctx.onEnter($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"button\", 43)(57, \"span\");\n          i0.ɵɵtext(58, \"Send\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_59_listener() {\n            return ctx.exportChat();\n          });\n          i0.ɵɵtext(60, \"Export Chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"button\", 45);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_61_listener() {\n            return ctx.exportDynamicChat();\n          });\n          i0.ɵɵtext(62, \"Export Dynamic Chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"button\", 46);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_63_listener() {\n            return ctx.copyChat();\n          });\n          i0.ɵɵtext(64, \"Copy Chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(65, ChatComponent_div_65_Template, 2, 0, \"div\", 47);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(32, _c3, ctx.isChatExpanded));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(34, _c4, ctx.loading || ctx.longTaskInProgress || ctx.agentTyping));\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading || ctx.longTaskInProgress ? \"Working\" : ctx.agentTyping ? \"Typing...\" : \"Ready\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedModel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getModelsByProvider(\"openai\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getModelsByProvider(\"deepseek\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getOtherModels());\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedLocalLlmModel);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.localLlmModels);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.streamingEnabled);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(36, _c4, ctx.autonomousMode));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.autonomousMode ? \"Autonomous\" : \"Assisted\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(38, _c4, ctx.showApiPayloads));\n          i0.ɵɵadvance(4);\n          i0.ɵɵattribute(\"aria-label\", ctx.isChatExpanded ? \"Collapse Chat\" : \"Expand Chat\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(40, _c5, ctx.isChatExpanded, !ctx.isChatExpanded));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.debugLogs.length > 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(48, 30, ctx.messages));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.agentTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.loading || ctx.messagesLoading) && !ctx.agentTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAgentThinking && ctx.agentThinkingContent);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.longTaskInProgress);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.subtasks && ctx.subtasks.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.messagesLoading || ctx.messagesSaving);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.messageForm.invalid || ctx.loading || ctx.messagesLoading || ctx.messagesSaving);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.messages.length === 0 || ctx.loading || ctx.messagesSaving);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.messages.length === 0 || ctx.loading || ctx.messagesSaving);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.messages.length === 0 || ctx.loading || ctx.messagesSaving);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.messagesSaving);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i5.DatePipe, i6.ReversePipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.progress-stages[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin: 20px 0;\\n  padding: 10px 20px;\\n  background: #f0f5ff;\\n  border-radius: 16px;\\n  position: relative;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  position: relative;\\n  z-index: 2;\\n  padding: 12px;\\n  border-radius: 50%;\\n  background: white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  opacity: 0.7;\\n  transition: all 0.3s ease;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: scale(1.05);\\n  box-shadow: 0 4px 12px rgba(79, 140, 255, 0.25);\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.active[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%] {\\n  background: #4f8cff;\\n  color: white;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.active[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2a5298;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.completed[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.completed[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%] {\\n  background: #22c55e;\\n  color: white;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.completed[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%] {\\n  color: #22c55e;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #e0e0e0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n  transition: all 0.3s ease;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 500;\\n  color: #666;\\n  white-space: nowrap;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-connector[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  height: 4px;\\n  background: #e0e0e0;\\n  margin: 0 -10px;\\n  position: relative;\\n  z-index: 1;\\n  transition: background 0.3s ease;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-connector.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #4f8cff, #22c55e);\\n}\\n\\n.progress-bar-container[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 12px;\\n  background: #f0f0f0;\\n  border-radius: 6px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%]   .progress-bar-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(to right, #4f8cff, #22c55e);\\n  border-radius: 6px;\\n  transition: width 0.5s ease;\\n  position: relative;\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%]   .progress-bar-fill.animated[_ngcontent-%COMP%] {\\n  background-size: 30px 30px;\\n  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\\n  animation: _ngcontent-%COMP%_animate-stripes 1s linear infinite;\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%]   .progress-bar-fill[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 8px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-label[_ngcontent-%COMP%] {\\n  display: block;\\n  text-align: center;\\n  margin-top: 6px;\\n  font-size: 14px;\\n  color: #555;\\n}\\n\\n@keyframes _ngcontent-%COMP%_animate-stripes {\\n  0% {\\n    background-position: 0 0;\\n  }\\n  100% {\\n    background-position: 30px 0;\\n  }\\n}\\n.subtasks-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 20px;\\n  margin-top: 20px;\\n  overflow-y: auto;\\n  padding: 0 20px 20px;\\n  max-height: 600px;\\n}\\n\\n.subtask-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 10px;\\n  border: 1px solid #eaeaea;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n}\\n.subtask-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-2px);\\n}\\n.subtask-card.subtask-error[_ngcontent-%COMP%] {\\n  border-color: #ffbaba;\\n  background-color: #fff8f8;\\n}\\n.subtask-card.subtask-error[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%] {\\n  background: #fff0f0;\\n  border-bottom-color: #ffbaba;\\n}\\n.subtask-card.subtask-error[_ngcontent-%COMP%]   .subtask-index[_ngcontent-%COMP%] {\\n  background: #ff5252;\\n}\\n.subtask-card.subtask-error[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ff5252;\\n}\\n.subtask-card.subtask-completed[_ngcontent-%COMP%] {\\n  border-color: #d0ead0;\\n}\\n.subtask-card.subtask-completed[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%] {\\n  background: #f0fff0;\\n  border-bottom-color: #d0ead0;\\n}\\n.subtask-card.subtask-completed[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #22c55e;\\n}\\n.subtask-card.subtask-active[_ngcontent-%COMP%] {\\n  border-color: #b3d7ff;\\n  box-shadow: 0 0 0 3px rgba(79, 140, 255, 0.2);\\n}\\n.subtask-card.subtask-active[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%] {\\n  background: #f0f7ff;\\n  border-bottom-color: #b3d7ff;\\n}\\n.subtask-card.subtask-active[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #4f8cff;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 16px 20px;\\n  background: #fafbff;\\n  border-bottom: 1px solid #eaeaea;\\n  position: relative;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-top-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  width: 100%;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-index[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 28px;\\n  height: 28px;\\n  background: #f0f5ff;\\n  border-radius: 50%;\\n  font-size: 13px;\\n  font-weight: bold;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  padding: 3px 8px;\\n  border-radius: 6px;\\n  background: #e0e0e0;\\n  color: #333;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  font-weight: 600;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.file[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  color: #1565c0;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.command[_ngcontent-%COMP%] {\\n  background: #e8f5e9;\\n  color: #2e7d32;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.browser_test[_ngcontent-%COMP%] {\\n  background: #fffde7;\\n  color: #f57f17;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.folder[_ngcontent-%COMP%] {\\n  background: #ede7f6;\\n  color: #4527a0;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-right-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-check-circle[_ngcontent-%COMP%] {\\n  color: #22c55e;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-exclamation-circle[_ngcontent-%COMP%] {\\n  color: #e53935;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-sync[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-circle-notch[_ngcontent-%COMP%] {\\n  color: #4f8cff;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-description[_ngcontent-%COMP%] {\\n  margin-bottom: 14px;\\n  font-size: 14px;\\n  color: #333;\\n  line-height: 1.4;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-description[_ngcontent-%COMP%]   .fa-arrow-right[_ngcontent-%COMP%] {\\n  color: #4f8cff;\\n  margin-top: 2px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  justify-content: space-between;\\n  flex-wrap: wrap;\\n  border-top: 1px solid #f0f0f0;\\n  padding-top: 10px;\\n  margin-top: 6px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .model-badge[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .web-research-badge[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .model-badge.openai[_ngcontent-%COMP%] {\\n  background: #dcfce7;\\n  color: #166534;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .model-badge.local[_ngcontent-%COMP%] {\\n  background: #e0f2fe;\\n  color: #075985;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .web-research-badge[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .web-research-badge[_ngcontent-%COMP%]:before {\\n  content: \\\"\\uD83C\\uDF10\\\";\\n  font-size: 12px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  padding: 4px 10px;\\n  background: #f44336;\\n  color: white;\\n  border: none;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n  transition: all 0.2s;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #d32f2f;\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: auto;\\n  align-items: center;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .feedback-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin-right: 4px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e0e0e0;\\n  cursor: pointer;\\n  color: #888;\\n  padding: 6px 10px;\\n  border-radius: 6px;\\n  transition: all 0.2s;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn[_ngcontent-%COMP%]:hover {\\n  background: #f5f5f5;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn.selected[_ngcontent-%COMP%]:first-child {\\n  color: white;\\n  background-color: #22c55e;\\n  border-color: #22c55e;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn.selected[_ngcontent-%COMP%]:last-child {\\n  color: white;\\n  background-color: #e53935;\\n  border-color: #e53935;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  font-weight: 500;\\n  color: #333;\\n  display: block;\\n  transition: background 0.2s ease;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]:hover {\\n  background: #f9f9f9;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]::-webkit-details-marker {\\n  display: none;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u25B6\\\";\\n  font-size: 10px;\\n  margin-right: 8px;\\n  display: inline-block;\\n  transition: transform 0.2s ease;\\n}\\ndetails[open][_ngcontent-%COMP%]   .subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]::before {\\n  transform: rotate(90deg);\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-top: 1px solid #f0f0f0;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #444;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   .copy-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #4f8cff;\\n  cursor: pointer;\\n  font-size: 14px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   .copy-btn[_ngcontent-%COMP%]:hover {\\n  color: #2a5298;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-output[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  font-family: monospace;\\n  font-size: 13px;\\n  line-height: 1.4;\\n  border-radius: 6px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n  margin: 0;\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-output[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n  color: #333;\\n  border: 1px solid #eaeaea;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  background: #fff5f5;\\n  color: #c53030;\\n  border: 1px solid #feb2b2;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .no-output[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  color: #666;\\n  font-style: italic;\\n  background: #f9f9f9;\\n  border-radius: 6px;\\n  text-align: center;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  background: #f9f9f9;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n  font-size: 13px;\\n  font-weight: 500;\\n  color: #444;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%]::-webkit-details-marker, .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%]::-webkit-details-marker {\\n  display: none;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   .web-results-content[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   .web-results-content[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 12px;\\n  border-radius: 6px;\\n  font-family: monospace;\\n  font-size: 12px;\\n  line-height: 1.4;\\n  max-height: 150px;\\n  overflow-y: auto;\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%]   .diff-added[_ngcontent-%COMP%] {\\n  color: #22c55e;\\n  background-color: #f0fff4;\\n  display: block;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%]   .diff-removed[_ngcontent-%COMP%] {\\n  color: #e53e3e;\\n  background-color: #fff5f5;\\n  display: block;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%]   .diff-hunk[_ngcontent-%COMP%] {\\n  color: #805ad5;\\n  background-color: #f8f0fc;\\n  display: block;\\n  padding: 2px 0;\\n  margin: 8px 0 4px 0;\\n}\\n\\n\\n\\n.feedback-thanks[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  font-size: 12px;\\n  color: #22c55e;\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n  padding: 4px 10px;\\n  border-radius: 4px;\\n  background-color: rgba(34, 197, 94, 0.1);\\n}\\n\\n\\n\\n.feedback-btns[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-left: auto;\\n}\\n\\n.feedback-label[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  color: #555;\\n  margin-right: 5px;\\n}\\n\\n\\n\\n.thumb-btn[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n  border: 1px solid #e0e0e0;\\n  padding: 6px 10px;\\n  border-radius: 6px;\\n  transition: all 0.2s;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n.thumb-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.thumb-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\n}\\n.thumb-btn.selected[_ngcontent-%COMP%]:first-child {\\n  color: white;\\n  background-color: #22c55e;\\n  border-color: #22c55e;\\n}\\n.thumb-btn.selected[_ngcontent-%COMP%]:last-child {\\n  color: white;\\n  background-color: #e53935;\\n  border-color: #e53935;\\n}\\n\\n\\n\\n\\n\\n.autonomous-workflow[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding: 15px;\\n  background: #f9fafc;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n\\n.workflow-header[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n  display: flex;\\n  align-items: center;\\n}\\n.workflow-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n\\n\\n.progress-stages[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 15px;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  opacity: 0.5;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #e8f0fe;\\n  border-radius: 50%;\\n  margin-bottom: 5px;\\n  font-size: 16px;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background: #4285f4;\\n  color: white;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon.active[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-connector[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  height: 2px;\\n  background: #e0e0e0;\\n  margin: 0 5px;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-connector.active[_ngcontent-%COMP%] {\\n  background: #4285f4;\\n}\\n\\n.progress-counter[_ngcontent-%COMP%] {\\n  text-align: right;\\n  font-size: 13px;\\n  color: #666;\\n  margin-bottom: 15px;\\n}\\n\\n\\n\\n.subtasks-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));\\n  gap: 15px;\\n  padding-bottom: 10px;\\n}\\n\\n.subtask-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n  border: 1px solid #eaeaea;\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.subtask-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\\n}\\n.subtask-card.running[_ngcontent-%COMP%] {\\n  border-color: #4285f4;\\n  box-shadow: 0 0 0 1px rgba(66, 133, 244, 0.2);\\n}\\n.subtask-card.completed[_ngcontent-%COMP%] {\\n  border-color: #34a853;\\n  box-shadow: 0 0 0 1px rgba(52, 168, 83, 0.2);\\n}\\n.subtask-card.error[_ngcontent-%COMP%] {\\n  border-color: #ea4335;\\n  box-shadow: 0 0 0 1px rgba(234, 67, 53, 0.2);\\n}\\n\\n\\n\\n.subtask-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 10px;\\n  background: #f4f8fc;\\n  border-bottom: 1px solid #e1e7ed;\\n  height: 40px;\\n}\\n.subtask-header[_ngcontent-%COMP%]   .subtask-number[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  background: #4285f4;\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 500;\\n  margin-right: 10px;\\n}\\n.subtask-header[_ngcontent-%COMP%]   .subtask-type[_ngcontent-%COMP%] {\\n  text-transform: uppercase;\\n  font-size: 11px;\\n  background: #e8f0fe;\\n  color: #1967d2;\\n  padding: 3px 8px;\\n  border-radius: 3px;\\n  letter-spacing: 0.5px;\\n  font-weight: 500;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.subtask-header[_ngcontent-%COMP%]   .model-label[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  font-size: 11px;\\n  padding: 2px 6px;\\n  border-radius: 3px;\\n  background: #1a73e8;\\n  color: white;\\n  font-weight: 500;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.subtask-header[_ngcontent-%COMP%]   .model-label.local[_ngcontent-%COMP%] {\\n  background: #9334e6;\\n}\\n.subtask-header[_ngcontent-%COMP%]   .hot-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  padding: 2px 6px;\\n  border-radius: 3px;\\n  background: #ea4335;\\n  color: white;\\n  font-weight: 500;\\n  margin-left: 5px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n\\n\\n.task-description[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 10px;\\n  border-bottom: 1px solid #e8f0fe;\\n  min-height: 40px;\\n}\\n.task-description[_ngcontent-%COMP%]   .bullet[_ngcontent-%COMP%] {\\n  color: #4285f4;\\n  font-size: 11px;\\n  margin-top: 2px;\\n  width: 12px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.task-description[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:last-child {\\n  font-size: 13px;\\n  line-height: 1.5;\\n  color: #202124;\\n  font-weight: 400;\\n  flex: 1;\\n}\\n\\n\\n\\n.output-area[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  flex-grow: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.output-area[_ngcontent-%COMP%]   .output-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 13px;\\n  margin-bottom: 6px;\\n  color: #202124;\\n}\\n.output-area[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%] {\\n  padding: 8px 10px;\\n  background: #f8f9fa;\\n  border-radius: 4px;\\n  font-family: \\\"Roboto Mono\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.5;\\n  color: #3c4043;\\n  max-height: 120px;\\n  min-height: 30px;\\n  overflow-y: auto;\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  border: 1px solid #e8eaed;\\n  margin-bottom: 8px;\\n}\\n.output-area[_ngcontent-%COMP%]   .output-content.error[_ngcontent-%COMP%] {\\n  background: #fce8e6;\\n  color: #c5221f;\\n  border-color: #f6bbb8;\\n}\\n.output-area[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%], .output-area[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #1a73e8;\\n  margin-top: 0px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  height: 24px;\\n}\\n.output-area[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]:before, .output-area[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]:before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 6px;\\n  height: 6px;\\n  background: #1a73e8;\\n  border-radius: 50%;\\n  margin-right: 5px;\\n}\\n.output-area[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]:hover, .output-area[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.feedback-row[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  border-top: 1px solid #e8f0fe;\\n  background: #f8f9fa;\\n  height: 36px;\\n}\\n.feedback-row[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #5f6368;\\n  margin-left: 3px;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 3px;\\n  border: 1px solid #e0e0e0;\\n  background: white;\\n  cursor: pointer;\\n  position: relative;\\n  padding: 0;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 1px;\\n  background: #4caf50;\\n  opacity: 0;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child.selected:after {\\n  opacity: 1;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 1px;\\n  background: #f44336;\\n  opacity: 0;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child.selected:after {\\n  opacity: 1;\\n}\\n\\n\\n\\n.retry-button[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 10px 10px 10px auto;\\n  padding: 4px 10px;\\n  background: #ea4335;\\n  color: white;\\n  border: none;\\n  border-radius: 3px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);\\n}\\n.retry-button[_ngcontent-%COMP%]:hover {\\n  background: #d32f2f;\\n  box-shadow: 0 2px 4px rgba(60, 64, 67, 0.3);\\n}\\n\\n\\n\\n\\n\\n.browser-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e3f2fd, #bbdefb) !important;\\n  border: 1px solid #90caf9 !important;\\n  border-radius: 12px !important;\\n  color: #0d47a1 !important;\\n}\\n\\n.browser-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2196f3, #64b5f6) !important;\\n  color: white !important;\\n}\\n\\n\\n\\n.openai-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f3e5f5, #e1bee7) !important;\\n  border: 1px solid #ce93d8 !important;\\n  border-radius: 12px !important;\\n  color: #4a148c !important;\\n}\\n\\n.openai-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9c27b0, #ba68c8) !important;\\n  color: white !important;\\n}\\n\\n\\n\\n.llm-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e9, #c8e6c9) !important;\\n  border: 1px solid #a5d6a7 !important;\\n  border-radius: 12px !important;\\n  color: #1b5e20 !important;\\n}\\n\\n.llm-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50, #81c784) !important;\\n  color: white !important;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffebee, #ffcdd2) !important;\\n  border: 1px solid #ef9a9a !important;\\n  border-radius: 12px !important;\\n  color: #b71c1c !important;\\n}\\n\\n.error-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f44336, #e57373) !important;\\n  color: white !important;\\n}\\n\\n\\n\\n.system-notification[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff8e1, #ffe0b2) !important;\\n  border: 1px solid #ffcc80 !important;\\n  border-radius: 12px !important;\\n  color: #e65100 !important;\\n}\\n\\n\\n\\n.message-metadata[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n\\n.execution-time[_ngcontent-%COMP%] {\\n  padding: 2px 6px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 4px;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.message-type[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  padding: 2px 6px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 4px;\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.user-message[_ngcontent-%COMP%]   .message-metadata[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .execution-time[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-type[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  background: linear-gradient(135deg, #fafdff 80%, #e3f0ff 100%);\\n  border-radius: 18px;\\n  box-shadow: 0 6px 32px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n  margin: 0 auto;\\n  transition: box-shadow 0.3s;\\n}\\n\\n.chat-container[_ngcontent-%COMP%]:focus-within, .chat-container[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.18);\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 5px 16px;\\n  background: #3378d1;\\n  color: white;\\n  border-top-left-radius: 8px;\\n  border-top-right-radius: 8px;\\n  height: 46px;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  animation: _ngcontent-%COMP%_fadeInDown 0.5s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.agent-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.agent-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 22px;\\n  font-weight: 700;\\n  letter-spacing: 0.5px;\\n  white-space: nowrap;\\n}\\n.agent-title[_ngcontent-%COMP%]   .agent-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-right: 4px;\\n}\\n.agent-title[_ngcontent-%COMP%]   .agent-status[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  background-color: rgba(255, 255, 255, 0.2);\\n  color: #fff;\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.agent-title[_ngcontent-%COMP%]   .agent-status.active[_ngcontent-%COMP%] {\\n  background-color: #22c55e;\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);\\n  }\\n}\\n.model-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  align-items: center;\\n  flex-wrap: wrap;\\n}\\n\\n.model-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  background: rgba(255, 255, 255, 0.18);\\n  padding: 7px 14px;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);\\n  transition: background 0.2s;\\n}\\n.model-selector[_ngcontent-%COMP%]   .autonomous-workflow[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  padding: 20px;\\n  background: linear-gradient(to bottom, #f5f7fa, #ffffff);\\n  border-radius: 12px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-out;\\n}\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 16px;\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #2a5298;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #4f8cff;\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   .workflow-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   .workflow-action-btn[_ngcontent-%COMP%] {\\n  background-color: #f0f5ff;\\n  border: 1px solid #d0e1ff;\\n  border-radius: 6px;\\n  padding: 6px 10px;\\n  color: #4f8cff;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   .workflow-action-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #e0edff;\\n  transform: translateY(-1px);\\n}\\n\\n.model-selector[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 15px;\\n  font-weight: 500;\\n  color: #2d3a4a;\\n}\\n\\n.model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  padding: 6px 12px 6px 32px;\\n  border-radius: 6px;\\n  border: 1.5px solid #b3d7ff;\\n  background: url('data:image/svg+xml;utf8,<svg fill=\\\"%234f8cff\\\" height=\\\"16\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"16\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M7 10l5 5 5-5z\\\"/></svg>') no-repeat 8px center/18px 18px, #fff;\\n  color: #333;\\n  font-size: 15px;\\n  transition: border 0.2s;\\n  appearance: none;\\n}\\n\\n.model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  border: 1.5px solid #4f8cff;\\n  outline: none;\\n}\\n\\n.clear-chat-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #e57373 60%, #ffb199 100%);\\n  border: none;\\n  color: #fff;\\n  padding: 7px 18px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 15px;\\n  font-weight: 600;\\n  transition: background 0.2s, color 0.2s, box-shadow 0.2s;\\n  margin-left: 10px;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.clear-chat-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDDD1\\\"; \\n\\n  font-size: 1.1em;\\n}\\n\\n.clear-chat-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, #ffb199 60%, #e57373 100%);\\n  color: #fffde7;\\n  box-shadow: 0 2px 8px rgba(229, 115, 115, 0.18);\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.mode-toggle-btn[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  border-radius: 16px;\\n  background-color: rgba(255, 255, 255, 0.15);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  color: white;\\n  font-size: 13px;\\n  font-weight: 600;\\n  transition: all 0.2s ease;\\n  min-width: 120px;\\n  text-align: center;\\n  white-space: nowrap;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.mode-toggle-btn[_ngcontent-%COMP%]   .fa-robot[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.mode-toggle-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.25);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\\n}\\n.mode-toggle-btn.active[_ngcontent-%COMP%] {\\n  background-color: #22c55e;\\n  border-color: #16a34a;\\n}\\n\\n.clear-chat-btn[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.15);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  padding: 6px 10px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  margin: 0 5px;\\n}\\n\\n.clear-chat-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 60, 60, 0.8);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\\n}\\n\\n.expand-chat-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.15);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  color: white;\\n  font-size: 14px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n  margin-left: 5px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.expand-chat-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.25);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  padding: 0 32px 0 32px;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column-reverse;\\n  gap: 18px;\\n  background: transparent;\\n  scrollbar-width: thin;\\n  scrollbar-color: #b3d7ff #fafdff;\\n  animation: _ngcontent-%COMP%_fadeIn 0.6s;\\n  max-height: 300px;\\n  min-height: 80px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #b3d7ff;\\n  border-radius: 4px;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #fafdff;\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: 12px;\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_slideIn 0.4s forwards;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.avatar[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #4f8cff 60%, #a0c4ff 100%);\\n  color: #fff;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  font-size: 18px;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);\\n  flex-shrink: 0;\\n  margin-bottom: 2px;\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffb199 60%, #e57373 100%);\\n}\\n\\n.system-message[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107 60%, #ff9800 100%);\\n}\\n\\n.bubble[_ngcontent-%COMP%] {\\n  padding: 14px 20px;\\n  border-radius: 16px;\\n  max-width: 420px;\\n  min-width: 60px;\\n  word-break: break-word;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  font-size: 16px;\\n  line-height: 1.6;\\n  position: relative;\\n  transition: background 0.2s;\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #ffb199 60%, #e57373 100%);\\n  color: #fff;\\n  border-bottom-right-radius: 4px;\\n  align-self: flex-end;\\n}\\n\\n.agent-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: #fafdff;\\n  color: #2d3a4a;\\n  border-bottom-left-radius: 4px;\\n  align-self: flex-start;\\n}\\n\\n.message-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 4px;\\n  font-size: 13px;\\n  opacity: 0.8;\\n}\\n\\n.sender[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n\\n.timestamp[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  line-height: 1.5;\\n  font-size: 15px;\\n}\\n\\n.message-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 14px;\\n  padding: 12px 32px 16px 32px;\\n  background: #fff;\\n  border-top: 1px solid #e0e0e0;\\n  border-bottom-left-radius: 8px;\\n  border-bottom-right-radius: 8px;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.03);\\n  align-items: flex-end;\\n}\\n\\n.message-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  resize: none;\\n  font-family: inherit;\\n  height: 40px;\\n  font-size: 14px;\\n  background: white;\\n}\\n\\n.message-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus {\\n  border: 1.5px solid #4f8cff;\\n  outline: none;\\n}\\n\\n.message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  background: #e0e0e0;\\n  color: #555;\\n  border: none;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: background 0.2s;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n.message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:not(:disabled):hover {\\n  background: #d0d0d0;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 180px;\\n  color: #888;\\n  text-align: center;\\n  font-size: 17px;\\n  opacity: 0.8;\\n}\\n\\n.loading-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  padding: 20px;\\n  color: #777;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  border: 3px solid rgba(0, 0, 0, 0.1);\\n  border-top: 3px solid #4f8cff;\\n  border-radius: 50%;\\n  width: 28px;\\n  height: 28px;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 900px) {\\n  .chat-header[_ngcontent-%COMP%], .message-form[_ngcontent-%COMP%], .messages-container[_ngcontent-%COMP%] {\\n    padding-left: 12px;\\n    padding-right: 12px;\\n  }\\n  .messages-container[_ngcontent-%COMP%] {\\n    max-height: 180px;\\n    min-height: 60px;\\n  }\\n}\\n@media (max-width: 600px) {\\n  .chat-header[_ngcontent-%COMP%], .message-form[_ngcontent-%COMP%], .messages-container[_ngcontent-%COMP%] {\\n    padding-left: 2px;\\n    padding-right: 2px;\\n  }\\n  .chat-header[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding-top: 10px;\\n    padding-bottom: 10px;\\n  }\\n  .message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    padding: 10px 16px;\\n    font-size: 15px;\\n  }\\n}\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  background: #ffffff;\\n  border-radius: 8px;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  min-height: 46px;\\n  overflow: visible;\\n}\\n\\n\\n\\n.message-form[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  gap: 10px;\\n  padding: 10px;\\n  background: white;\\n  position: sticky;\\n  bottom: 0;\\n  z-index: 1000;\\n  align-items: center;\\n  width: 100%;\\n  flex-wrap: wrap;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%]:not(.expanded)   .messages-container[_ngcontent-%COMP%], .chat-container[_ngcontent-%COMP%]:not(.expanded)   .autonomous-workflow[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n\\n\\n.file-changes-container[_ngcontent-%COMP%] {\\n  display: none !important;\\n}\\n\\n\\n\\n.chat-container.expanded[_ngcontent-%COMP%] {\\n  height: auto;\\n  max-height: 600px;\\n  \\n\\n}\\n\\n\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-height: 150px;\\n  overflow-y: auto;\\n  transition: all 0.3s;\\n}\\n\\n.chat-container.expanded[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n}\\n\\n\\n\\n.chat-container.expanded[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%] {\\n  border-radius: 10px 10px 0 0;\\n  position: sticky;\\n  top: 0;\\n  z-index: 10;\\n}\\n\\n\\n\\n.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  flex-wrap: wrap;\\n  flex: 1;\\n  margin: 0 20px;\\n  \\n\\n}\\n.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: rgba(255, 255, 255, 0.15);\\n  padding: 4px 12px;\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  margin-right: 10px;\\n}\\n.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 600;\\n  white-space: nowrap;\\n}\\n.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: none;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-size: 13px;\\n  min-width: 140px;\\n}\\n.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\\n  background: #fff;\\n  color: #333;\\n}\\n\\n\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n\\n\\n.expand-chat-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4f8cff 80%, #a0c4ff 100%);\\n  color: white;\\n  opacity: 1 !important;\\n  visibility: visible !important;\\n}\\n\\n\\n\\n.file-changes-wrapper[_ngcontent-%COMP%] {\\n  margin: 10px 0;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  background: #f8f9fa;\\n  width: 100%;\\n}\\n\\n.file-changes[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  width: 100%;\\n}\\n\\n.file-changes-header[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  background: #f1f3f4;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.file-diff[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  font-family: \\\"Roboto Mono\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.5;\\n  white-space: pre-wrap;\\n  overflow-x: auto;\\n  max-height: 300px;\\n  overflow-y: auto;\\n  background: #f8f9fa;\\n  color: #333;\\n  border: 1px solid #eee;\\n  margin: 0;\\n}\\n\\n\\n\\n.diff-added[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  background-color: #e6ffec;\\n  display: block;\\n}\\n\\n.diff-removed[_ngcontent-%COMP%] {\\n  color: #d73a49;\\n  background-color: #ffeef0;\\n  display: block;\\n}\\n\\n.diff-hunk[_ngcontent-%COMP%] {\\n  color: #0366d6;\\n  background-color: #f1f8ff;\\n  display: block;\\n  font-weight: bold;\\n}\\n\\n\\n\\n.chat-container.expanded[_ngcontent-%COMP%] {\\n  max-height: none;\\n  height: auto;\\n  position: relative;\\n}\\n\\n.chat-container.expanded[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%], .chat-container.expanded[_ngcontent-%COMP%]   .autonomous-workflow[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  position: relative !important;\\n  z-index: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);\\n  min-height: 150px; \\n\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between; \\n\\n}\\n\\n.expand-chat-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4f8cff 80%, #a0c4ff 100%);\\n  color: #fff;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 18px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  margin-left: 10px;\\n  padding: 6px 14px;\\n  transition: background 0.2s, box-shadow 0.2s;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.expand-chat-btn[_ngcontent-%COMP%]:hover {\\n  background: #4f8cff;\\n  box-shadow: 0 2px 8px rgba(79, 140, 255, 0.18);\\n}\\n\\n.export-chat-btn[_ngcontent-%COMP%], .copy-chat-btn[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  padding: 6px 14px;\\n  background: #1976d2;\\n  color: #fff;\\n  border: none;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  transition: background 0.2s;\\n}\\n\\n.export-chat-btn[_ngcontent-%COMP%]:disabled, .copy-chat-btn[_ngcontent-%COMP%]:disabled {\\n  background: #bdbdbd;\\n  cursor: not-allowed;\\n}\\n\\n.export-chat-btn[_ngcontent-%COMP%]:hover:not(:disabled), .copy-chat-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #1565c0;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 10px 20px;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  background-color: #4f8cff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing-animation 1.4s infinite ease-in-out;\\n  opacity: 0.7;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing-animation {\\n  0%, 100% {\\n    transform: scale(0.7);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: scale(1.2);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.message.streaming[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  border-right: 2px solid #4f8cff;\\n  animation: _ngcontent-%COMP%_cursor-blink 1s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0%, 100% {\\n    border-color: transparent;\\n  }\\n  50% {\\n    border-color: #4f8cff;\\n  }\\n}\\n\\n\\n.message-reactions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 5px 0;\\n}\\n\\n.reaction-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 6px;\\n}\\n\\n.reaction-button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 16px;\\n  padding: 4px 8px;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n.reaction-button[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  transform: translateY(-1px);\\n}\\n.reaction-button.active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border-color: #bbdefb;\\n  color: #1976d2;\\n}\\n\\n\\n\\n.agent-thinking-panel[_ngcontent-%COMP%] {\\n  margin: 10px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.thinking-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 15px;\\n  background-color: #f5f5f5;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.thinking-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.thinking-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #666;\\n  cursor: pointer;\\n  font-size: 14px;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n}\\n.thinking-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: #e0e0e0;\\n}\\n\\n.thinking-content[_ngcontent-%COMP%] {\\n  padding: 10px 15px;\\n  background-color: #fafafa;\\n  font-family: monospace;\\n  font-size: 14px;\\n  line-height: 1.5;\\n  overflow-x: auto;\\n  margin: 0;\\n  white-space: pre-wrap;\\n}\\n\\n\\n\\n.memory-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4f8cff 0%, #2979ff 100%);\\n  border: none;\\n  color: #fff;\\n  padding: 7px 12px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 15px;\\n  font-weight: 600;\\n  transition: all 0.2s;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.memory-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, #2979ff 0%, #1565c0 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(41, 121, 255, 0.2);\\n}\\n.memory-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n\\n\\n.streaming-toggle[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.18);\\n  padding: 5px 10px;\\n  border-radius: 8px;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n}\\n.streaming-toggle[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n.streaming-toggle[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  accent-color: #22c55e;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.saving-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -20px;\\n  left: 10px;\\n  font-size: 12px;\\n  color: #888;\\n  background-color: rgba(255, 255, 255, 0.7);\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0.6;\\n  }\\n}\\n\\n\\n.api-toggle-btn[_ngcontent-%COMP%] {\\n  background-color: #4a4a4a;\\n  color: #fff;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 8px;\\n  margin-right: 5px;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n.api-toggle-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #666;\\n}\\n.api-toggle-btn.active[_ngcontent-%COMP%] {\\n  background-color: #3498db;\\n}\\n\\n\\n\\n.message.api-request-message[_ngcontent-%COMP%], .message.api-response-message[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-left: 3px solid #3498db;\\n}\\n.message.api-request-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%], .message.api-response-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e0e0e0;\\n}\\n.message.api-request-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%], .message.api-response-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  padding: 10px;\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  font-family: \\\"Consolas\\\", \\\"Monaco\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.4;\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n.message.api-request-message[_ngcontent-%COMP%] {\\n  border-left-color: #3498db;\\n}\\n.message.api-request-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  border-left: 3px solid #3498db;\\n}\\n.message.api-response-message[_ngcontent-%COMP%] {\\n  border-left-color: #2ecc71;\\n}\\n.message.api-response-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  border-left: 3px solid #2ecc71;\\n}\\n\\n\\n\\n.api-request-avatar[_ngcontent-%COMP%], .api-response-avatar[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #333;\\n}\\n.api-request-avatar[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .api-response-avatar[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.api-request-avatar[_ngcontent-%COMP%] {\\n  background-color: #d4e6f1;\\n}\\n\\n.api-response-avatar[_ngcontent-%COMP%] {\\n  background-color: #d5f5e3;\\n}\\n\\n\\n\\n.debug-logs[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 4px;\\n  margin: 10px 0;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 12px;\\n  background: #e9ecef;\\n  border-bottom: 1px solid #dee2e6;\\n  font-weight: bold;\\n  font-size: 12px;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-header[_ngcontent-%COMP%]   .clear-logs-btn[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  color: white;\\n  border: none;\\n  padding: 4px 8px;\\n  border-radius: 3px;\\n  font-size: 10px;\\n  cursor: pointer;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-header[_ngcontent-%COMP%]   .clear-logs-btn[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%] {\\n  padding: 8px;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 2px 0;\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 11px;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry[_ngcontent-%COMP%]   .log-time[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  min-width: 60px;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry[_ngcontent-%COMP%]   .log-level[_ngcontent-%COMP%] {\\n  min-width: 50px;\\n  font-weight: bold;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry.log-info[_ngcontent-%COMP%]   .log-level[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry.log-error[_ngcontent-%COMP%]   .log-level[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry.log-warning[_ngcontent-%COMP%]   .log-level[_ngcontent-%COMP%] {\\n  color: #fd7e14;\\n}\\n.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry[_ngcontent-%COMP%]   .log-message[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: #212529;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "model_r14", "id", "ɵɵadvance", "ɵɵtextInterpolate", "name", "model_r15", "model_r16", "llm_r17", "log_r19", "level", "ɵɵpipeBind2", "timestamp", "toUpperCase", "message", "ɵɵlistener", "ChatComponent_div_43_Template_button_click_4_listener", "ɵɵrestoreView", "_r21", "ctx_r20", "ɵɵnextContext", "ɵɵresetView", "clearDebugLogs", "ɵɵtemplate", "ChatComponent_div_43_div_7_Template", "ctx_r4", "debugLogs", "slice", "message_r22", "metadata", "modelId", "ɵɵelement", "ɵɵtextInterpolate1", "executionTime", "ChatComponent_div_47_span_2_Template", "ChatComponent_div_47_span_3_Template", "ChatComponent_div_47_span_4_Template", "ChatComponent_div_47_span_5_Template", "ChatComponent_div_47_span_6_Template", "ChatComponent_div_47_span_7_Template", "ChatComponent_div_47_span_8_Template", "ChatComponent_div_47_span_9_Template", "ChatComponent_div_47_span_10_Template", "ChatComponent_div_47_span_11_Template", "ChatComponent_div_47_span_15_Template", "ChatComponent_div_47_span_16_Template", "ChatComponent_div_47_span_17_Template", "ChatComponent_div_47_span_18_Template", "ChatComponent_div_47_span_19_Template", "ChatComponent_div_47_span_20_Template", "ChatComponent_div_47_span_21_Template", "ChatComponent_div_47_span_22_Template", "ChatComponent_div_47_span_23_Template", "ChatComponent_div_47_span_24_Template", "ChatComponent_div_47_span_25_Template", "ChatComponent_div_47_span_26_Template", "ChatComponent_div_47_span_27_Template", "ChatComponent_div_47_span_28_Template", "ChatComponent_div_47_span_29_Template", "ChatComponent_div_47_span_33_Template", "ChatComponent_div_47_span_34_Template", "ChatComponent_div_47_div_36_Template", "ChatComponent_div_47_Template_button_click_39_listener", "restoredCtx", "_r57", "$implicit", "ctx_r56", "addReaction", "ChatComponent_div_47_span_41_Template", "ChatComponent_div_47_Template_button_click_42_listener", "ctx_r58", "ChatComponent_div_47_span_44_Template", "ChatComponent_div_47_Template_button_click_45_listener", "ctx_r59", "ChatComponent_div_47_span_47_Template", "ɵɵpureFunctionV", "_c1", "sender", "messageType", "isComplete", "_c2", "content", "ɵɵsanitizeHtml", "ɵɵclassProp", "reactions", "includes", "ChatComponent_div_51_Template_button_click_4_listener", "_r61", "ctx_r60", "toggleAgentThinking", "ctx_r10", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ChatComponent_div_53_div_31_div_21_Template_button_click_4_listener", "_r74", "i_r64", "index", "ctx_r72", "setSubtaskFeedback", "ChatComponent_div_53_div_31_div_21_Template_button_click_5_listener", "ctx_r75", "subtask_r63", "feedback", "ChatComponent_div_53_div_31_button_22_Template_button_click_0_listener", "_r80", "ctx_r78", "retrySubtask", "ChatComponent_div_53_div_31_div_6_Template", "ChatComponent_div_53_div_31_div_7_Template", "ChatComponent_div_53_div_31_div_8_Template", "ChatComponent_div_53_div_31_div_19_Template", "ChatComponent_div_53_div_31_div_20_Template", "ChatComponent_div_53_div_31_div_21_Template", "ChatComponent_div_53_div_31_button_22_Template", "subtask", "type", "model_type", "web_research_used", "description", "error", "result", "web_results", "file_diff", "completed", "ChatComponent_div_53_div_31_Template", "ctx_r12", "currentStage", "ɵɵtextInterpolate2", "completedSubtasks", "subtasks", "length", "ChatComponent", "constructor", "fb", "socketService", "projectService", "agentService", "projectName", "messagesLoading", "messagesSaving", "messageEvent", "chatExpandChange", "messages", "loading", "models", "selected<PERSON><PERSON>l", "localLlmModels", "selectedLocalLlmModel", "isChatExpanded", "autonomousMode", "agentTyping", "showAgentThinking", "streamingEnabled", "showApiPayloads", "apiRequests", "apiResponses", "ngOnInit", "console", "log", "initForm", "loadMessages", "loadModels", "setupSocketListeners", "ngAfterViewChecked", "scrollToBottom", "messagesContainer", "nativeElement", "scrollTop", "scrollHeight", "err", "messageForm", "group", "required", "warn", "getProjectMessages", "subscribe", "response", "getModels", "getModelsByProvider", "provider", "filter", "model", "startsWith", "getOtherModels", "knownProviders", "some", "on", "data", "addDebugLog", "substring", "project_name", "m", "isAgentWorkingPlaceholder", "message_type", "push", "message_id", "Date", "now", "is_typing", "lastAgentMessage", "find", "token", "for<PERSON>ach", "thinking", "messageToUpdate", "reaction", "JSON", "stringify", "sendMessage", "invalid", "messageContent", "get", "value", "userMessageId", "requestPayload", "model_id", "local_llm_model_id", "streaming_enabled", "requestEntry", "endpoint", "payload", "reset", "responseEntry", "errorEntry", "onModelChange", "deleteMessage", "splice", "clearChat", "deleteProjectMessages", "toggleChatExpand", "emit", "onEnter", "event", "shift<PERSON>ey", "preventDefault", "exportChat", "exportProjectChat", "alert", "file_path", "detail", "exportDynamicChat", "chatText", "map", "join", "blob", "Blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "toISOString", "replace", "filename", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "copyChat", "navigator", "clipboard", "writeText", "then", "onLocalLlmModelChange", "longTaskInProgress", "s", "toggleAutonomousMode", "formatFileDiff", "diff", "escape", "c", "split", "line", "every", "stage", "Math", "max", "messageId", "addMessageReaction", "r", "resetContextMemory", "toggleApiPayloads", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "SocketService", "i3", "ProjectService", "i4", "AgentService", "selectors", "viewQuery", "ChatComponent_Query", "rf", "ctx", "ChatComponent_Template_select_ngModelChange_14_listener", "$event", "ChatComponent_Template_select_change_14_listener", "ChatComponent_option_16_Template", "ChatComponent_option_18_Template", "ChatComponent_option_19_Template", "ChatComponent_Template_select_ngModelChange_24_listener", "ChatComponent_Template_select_change_24_listener", "ChatComponent_option_25_Template", "ChatComponent_Template_input_ngModelChange_28_listener", "ChatComponent_Template_button_click_31_listener", "ChatComponent_Template_button_click_35_listener", "ChatComponent_Template_button_click_37_listener", "ChatComponent_Template_button_click_39_listener", "ChatComponent_Template_button_click_41_listener", "ChatComponent_div_43_Template", "ChatComponent_div_46_Template", "ChatComponent_div_47_Template", "ChatComponent_div_49_Template", "ChatComponent_div_50_Template", "ChatComponent_div_51_Template", "ChatComponent_div_52_Template", "ChatComponent_div_53_Template", "ChatComponent_Template_form_ngSubmit_54_listener", "ChatComponent_Template_textarea_keydown_enter_55_listener", "ChatComponent_Template_button_click_59_listener", "ChatComponent_Template_button_click_61_listener", "ChatComponent_Template_button_click_63_listener", "ChatComponent_div_65_Template", "ɵɵpureFunction1", "_c3", "_c4", "ɵɵattribute", "ɵɵpureFunction2", "_c5", "ɵɵpipeBind1"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\chat\\chat.component.ts", "C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\chat\\chat.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { SocketService } from '../../services/socket.service';\r\nimport { ProjectService } from '../../services/project.service';\r\nimport { AgentService } from '../../services/agent.service';\r\n\r\ninterface MessageData {\r\n  project_name: string;\r\n  message: string;\r\n  message_id?: string;\r\n  message_type?: string;\r\n}\r\n\r\ninterface CompleteData {\r\n  project_name: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-chat',\r\n  templateUrl: './chat.component.html',\r\n  styleUrls: ['./chat.component.scss']\r\n})\r\nexport class ChatComponent implements OnInit, AfterViewChecked {\r\n  @Input() projectName: string = '';\r\n  @Input() messagesLoading: boolean = false;\r\n  @Input() messagesSaving: boolean = false;\r\n  @Output() messageEvent = new EventEmitter<any>();\r\n  @Output() chatExpandChange = new EventEmitter<boolean>();\r\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\r\n\r\n  messageForm!: FormGroup;\r\n  messages: any[] = [];\r\n  loading = false;\r\n  models: any[] = [];\r\n  selectedModel: string = 'deepseek/deepseek-coder';\r\n  localLlmModels: any[] = [\r\n    { id: 'mistral-nemo-instruct-2407', name: 'Mistral Nemo Instruct 2407' },\r\n    // Add more local LLM models here if needed\r\n  ];\r\n  selectedLocalLlmModel: string = 'mistral-nemo-instruct-2407';\r\n  isChatExpanded: boolean = false;\r\n  subtasks: any[] = [];\r\n  autonomousMode: boolean = false;\r\n  agentTyping: boolean = false;\r\n  agentThinkingContent: string = '';\r\n  showAgentThinking: boolean = false;\r\n  streamingEnabled: boolean = true;\r\n  showApiPayloads: boolean = true;\r\n  apiRequests: any[] = [];\r\n  apiResponses: any[] = [];\r\n  debugLogs: any[] = [];\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private socketService: SocketService,\r\n    private projectService: ProjectService,\r\n    private agentService: AgentService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    console.log('[ChatComponent] ngOnInit called');\r\n    this.initForm();\r\n    this.loadMessages();\r\n    this.loadModels();\r\n    this.setupSocketListeners();\r\n  }\r\n\r\n  ngAfterViewChecked(): void {\r\n    this.scrollToBottom();\r\n  }\r\n\r\n  scrollToBottom(): void {\r\n    try {\r\n      if (this.messagesContainer && this.messagesContainer.nativeElement) {\r\n        this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\r\n      }\r\n    } catch (err) {}\r\n  }\r\n\r\n  initForm(): void {\r\n    console.log('[ChatComponent] Initializing form');\r\n    this.messageForm = this.fb.group({\r\n      message: ['', Validators.required]\r\n    });\r\n  }\r\n\r\n  loadMessages(): void {\r\n    console.log('[ChatComponent] loadMessages called for project:', this.projectName);\r\n    if (!this.projectName) {\r\n      console.warn('[ChatComponent] No projectName found. Skipping loadMessages.');\r\n      return;\r\n    }\r\n\r\n    if (!this.messagesLoading) {\r\n      this.loading = true;\r\n    }\r\n\r\n    this.projectService.getProjectMessages(this.projectName).subscribe(\r\n      (response: any) => {\r\n        console.log('[ChatComponent] Project messages loaded:', response);\r\n        this.messages = response.messages || [];\r\n        this.loading = false;\r\n      },\r\n      (error: any) => {\r\n        console.error('[ChatComponent] ❌ Error loading messages:', error);\r\n        this.loading = false;\r\n      }\r\n    );\r\n  }\r\n\r\n  loadModels(): void {\r\n    console.log('[ChatComponent] loadModels called');\r\n    this.agentService.getModels().subscribe(\r\n      (response: any) => {\r\n        console.log('[ChatComponent] Models loaded:', response);\r\n        this.models = response.models || [];\r\n      },\r\n      (error: any) => {\r\n        console.error('[ChatComponent] ❌ Error loading models:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets models filtered by provider\r\n   */\r\n  getModelsByProvider(provider: string): any[] {\r\n    return this.models.filter(model => model.id.startsWith(`${provider}/`));\r\n  }\r\n\r\n  /**\r\n   * Gets models that don't belong to specified providers\r\n   */\r\n  getOtherModels(): any[] {\r\n    const knownProviders = ['openai', 'deepseek'];\r\n    return this.models.filter(model => \r\n      !knownProviders.some(provider => model.id.startsWith(`${provider}/`))\r\n    );\r\n  }\r\n\r\n  setupSocketListeners(): void {\r\n    console.log('[ChatComponent] Setting up socket listeners');\r\n\r\n    this.socketService.on('agent_message').subscribe((data: MessageData) => {\r\n      console.log('[ChatComponent] 🔁 Received socket \"agent_message\":', data);\r\n      this.addDebugLog('info', `Agent message received: ${data.message?.substring(0, 50)}...`);\r\n\r\n      if (data.project_name === this.projectName) {\r\n        this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\r\n\r\n        // Determine the message type from incoming data\r\n        const messageType = data.message_type || 'agent';\r\n\r\n        this.messages.push({\r\n          id: data.message_id || `msg-${Date.now()}`,\r\n          sender: 'agent',\r\n          content: data.message,\r\n          timestamp: new Date(),\r\n          isAgentWorkingPlaceholder: false,\r\n          messageType: messageType,\r\n          reactions: []\r\n        });\r\n        this.loading = false;\r\n        console.log('[ChatComponent] Message added to chat from agent');\r\n      }\r\n    });\r\n\r\n    this.socketService.on('agent_typing').subscribe((data: any) => {\r\n      console.log('[ChatComponent] 🔁 Received socket \"agent_typing\":', data);\r\n\r\n      if (data.project_name === this.projectName) {\r\n        this.agentTyping = data.is_typing;\r\n        // If not typing anymore, remove any placeholder messages\r\n        if (!data.is_typing) {\r\n          this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\r\n        }\r\n      }\r\n    });\r\n\r\n    this.socketService.on('agent_stream_token').subscribe((data: any) => {\r\n      console.log('[ChatComponent] 🔁 Received socket \"agent_stream_token\":', data);\r\n\r\n      if (data.project_name === this.projectName) {\r\n        // Find the last agent message or create a new one if none exists\r\n        let lastAgentMessage = this.messages.find(m => m.sender === 'agent' && !m.isComplete);\r\n        \r\n        if (!lastAgentMessage) {\r\n          lastAgentMessage = {\r\n            id: `stream-${Date.now()}`,\r\n            sender: 'agent',\r\n            content: '',\r\n            timestamp: new Date(),\r\n            isComplete: false,\r\n            reactions: []\r\n          };\r\n          this.messages.push(lastAgentMessage);\r\n        }\r\n        \r\n        // Append the token to the message content\r\n        lastAgentMessage.content += data.token;\r\n        this.scrollToBottom();\r\n      }\r\n    });\r\n\r\n    this.socketService.on('agent_complete').subscribe((data: CompleteData) => {\r\n      console.log('[ChatComponent] ✅ Received socket \"agent_complete\":', data);\r\n\r\n      if (data.project_name === this.projectName) {\r\n        this.loading = false;\r\n        this.agentTyping = false;\r\n        \r\n        // Mark all agent messages as complete\r\n        this.messages.forEach(message => {\r\n          if (message.sender === 'agent') {\r\n            message.isComplete = true;\r\n          }\r\n        });\r\n        \r\n        // Remove any placeholder messages\r\n        this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\r\n        console.log('[ChatComponent] Loading state cleared after agent_complete');\r\n      }\r\n    });\r\n    \r\n    this.socketService.on('agent_thinking').subscribe((data: any) => {\r\n      console.log('[ChatComponent] 🧠 Received socket \"agent_thinking\":', data);\r\n      \r\n      if (data.project_name === this.projectName && this.showAgentThinking) {\r\n        // Display the agent's thought process in a special UI element if debugging is enabled\r\n        this.agentThinkingContent = data.thinking;\r\n      }\r\n    });\r\n    \r\n    this.socketService.on('message_reaction').subscribe((data: any) => {\r\n      console.log('[ChatComponent] 👍 Received socket \"message_reaction\":', data);\r\n      \r\n      if (data.project_name === this.projectName) {\r\n        // Find the message and add the reaction\r\n        const messageToUpdate = this.messages.find(m => m.id === data.message_id);\r\n        if (messageToUpdate) {\r\n          if (!messageToUpdate.reactions) {\r\n            messageToUpdate.reactions = [];\r\n          }\r\n          if (!messageToUpdate.reactions.includes(data.reaction)) {\r\n            messageToUpdate.reactions.push(data.reaction);\r\n          }\r\n        }\r\n      }\r\n    });\r\n\r\n    // Listen for any error events\r\n    this.socketService.on('error').subscribe((data: any) => {\r\n      console.error('[ChatComponent] ❌ Socket error:', data);\r\n      this.addDebugLog('error', `Socket error: ${JSON.stringify(data)}`);\r\n    });\r\n\r\n    // Listen for agent errors specifically\r\n    this.socketService.on('agent_error').subscribe((data: any) => {\r\n      console.error('[ChatComponent] ❌ Agent error:', data);\r\n      this.addDebugLog('error', `Agent error: ${data.error || 'Unknown error'}`);\r\n\r\n      if (data.project_name === this.projectName) {\r\n        this.loading = false;\r\n        this.agentTyping = false;\r\n\r\n        // Add error message to chat\r\n        this.messages.push({\r\n          id: `error-${Date.now()}`,\r\n          sender: 'system',\r\n          messageType: 'error',\r\n          content: `<strong>Agent Error:</strong><br>${data.error || 'Unknown error occurred'}`,\r\n          timestamp: new Date(),\r\n          reactions: []\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  sendMessage(): void {\r\n    if (this.messageForm.invalid) {\r\n      return;\r\n    }\r\n    \r\n    const messageContent = this.messageForm.get('message')?.value;\r\n    if (!messageContent || !this.projectName) {\r\n      return;\r\n    }\r\n    \r\n    // Add user message to the chat\r\n    const userMessageId = `msg-${Date.now()}`;\r\n    this.messages.push({\r\n      id: userMessageId,\r\n      sender: 'user',\r\n      content: messageContent,\r\n      timestamp: new Date(),\r\n      reactions: []\r\n    });\r\n    \r\n    // Create request payload\r\n    const requestPayload = {\r\n      project_name: this.projectName,\r\n      message: messageContent,\r\n      model_id: this.selectedModel,\r\n      local_llm_model_id: this.selectedLocalLlmModel,\r\n      streaming_enabled: this.streamingEnabled\r\n    };\r\n    \r\n    // Store request\r\n    const requestEntry = {\r\n      timestamp: new Date(),\r\n      type: 'request',\r\n      endpoint: `/projects/${this.projectName}/messages`,\r\n      payload: requestPayload\r\n    };\r\n    this.apiRequests.push(requestEntry);\r\n    \r\n    // If API payloads are visible, add to messages\r\n    if (this.showApiPayloads) {\r\n      this.messages.push({\r\n        id: `api-req-${Date.now()}`,\r\n        sender: 'system',\r\n        messageType: 'api_request',\r\n        content: `<strong>API Request:</strong><br><pre>${JSON.stringify(requestPayload, null, 2)}</pre>`,\r\n        timestamp: new Date(),\r\n        reactions: []\r\n      });\r\n    }\r\n    \r\n    // Reset the form\r\n    this.messageForm.reset();\r\n    \r\n    // Show loading indicator\r\n    this.loading = true;\r\n    \r\n    // Send to API\r\n    this.addDebugLog('info', `Sending message with model: ${this.selectedModel}`);\r\n    if (this.streamingEnabled) {\r\n      // For streaming, we handle via sockets\r\n      this.addDebugLog('info', 'Using streaming mode via WebSocket');\r\n      this.socketService.sendMessage(this.projectName, messageContent, this.selectedModel);\r\n    } else {\r\n      // For non-streaming, we make a direct API call\r\n      this.agentService.sendMessage(\r\n        this.projectName, \r\n        messageContent, \r\n        this.selectedModel,\r\n        this.selectedLocalLlmModel,\r\n        false\r\n      ).subscribe(\r\n        (response: any) => {\r\n          // Store response\r\n          const responseEntry = {\r\n            timestamp: new Date(),\r\n            type: 'response',\r\n            endpoint: `/projects/${this.projectName}/messages`,\r\n            payload: response\r\n          };\r\n          this.apiResponses.push(responseEntry);\r\n          \r\n          // If API payloads are visible, add to messages\r\n          if (this.showApiPayloads) {\r\n            this.messages.push({\r\n              id: `api-res-${Date.now()}`,\r\n              sender: 'system',\r\n              messageType: 'api_response',\r\n              content: `<strong>API Response:</strong><br><pre>${JSON.stringify(response, null, 2)}</pre>`,\r\n              timestamp: new Date(),\r\n              reactions: []\r\n            });\r\n          }\r\n          \r\n          this.loading = false;\r\n        },\r\n        (error: any) => {\r\n          console.error('[ChatComponent] ❌ Error sending message:', error);\r\n          \r\n          // Store error response\r\n          const errorEntry = {\r\n            timestamp: new Date(),\r\n            type: 'error',\r\n            endpoint: `/projects/${this.projectName}/messages`,\r\n            payload: error\r\n          };\r\n          this.apiResponses.push(errorEntry);\r\n          \r\n          // Add error message\r\n          this.messages.push({\r\n            id: `error-${Date.now()}`,\r\n            sender: 'system',\r\n            messageType: 'error',\r\n            content: `<strong>API Error:</strong><br><pre>${JSON.stringify(error, null, 2)}</pre>`,\r\n            timestamp: new Date(),\r\n            reactions: []\r\n          });\r\n          \r\n          this.loading = false;\r\n        }\r\n      );\r\n    }\r\n  }\r\n\r\n  onModelChange(modelId: string): void {\r\n    console.log('[ChatComponent] Model changed to:', modelId);\r\n    this.selectedModel = modelId;\r\n  }\r\n\r\n  deleteMessage(index: number): void {\r\n    this.messages.splice(index, 1);\r\n  }\r\n\r\n  clearChat(): void {\r\n    if (!this.projectName) return;\r\n    // Clear messages in UI\r\n    this.messages = [];\r\n    // Call backend API to delete chat history\r\n    this.projectService.deleteProjectMessages(this.projectName).subscribe(\r\n      () => {\r\n        console.log('[ChatComponent] Chat history deleted on backend');\r\n      },\r\n      (error) => {\r\n        console.error('[ChatComponent] Error deleting chat history:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n  toggleChatExpand(): void {\r\n    this.isChatExpanded = !this.isChatExpanded;\r\n    this.chatExpandChange.emit(this.isChatExpanded);\r\n  }\r\n\r\n  onEnter(event: KeyboardEvent): void {\r\n    if (event.shiftKey) {\r\n      return; // allow newline\r\n    }\r\n    event.preventDefault();\r\n    this.sendMessage();\r\n  }\r\n\r\n  exportChat(): void {\r\n    if (!this.projectName) return;\r\n    this.projectService.exportProjectChat(this.projectName).subscribe(\r\n      (response: any) => {\r\n        alert('Chat exported: ' + (response?.file_path || 'Success'));\r\n      },\r\n      (error: any) => {\r\n        alert('Failed to export chat: ' + (error?.error?.detail || error));\r\n      }\r\n    );\r\n  }\r\n\r\n  exportDynamicChat(): void {\r\n    if (!this.messages.length) return;\r\n    // Format chat as text\r\n    const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\\n');\r\n    const blob = new Blob([chatText], { type: 'text/plain' });\r\n    const url = window.URL.createObjectURL(blob);\r\n    const a = document.createElement('a');\r\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\r\n    const filename = `${this.projectName || 'chat'}-dynamic-${timestamp}.txt`;\r\n    a.href = url;\r\n    a.download = filename;\r\n    document.body.appendChild(a);\r\n    a.click();\r\n    setTimeout(() => {\r\n      document.body.removeChild(a);\r\n      window.URL.revokeObjectURL(url);\r\n    }, 0);\r\n  }\r\n\r\n  copyChat(): void {\r\n    const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\\n');\r\n    navigator.clipboard.writeText(chatText).then(\r\n      () => alert('Chat copied to clipboard!'),\r\n      () => alert('Failed to copy chat to clipboard.')\r\n    );\r\n  }\r\n\r\n  onLocalLlmModelChange(modelId: string): void {\r\n    this.selectedLocalLlmModel = modelId;\r\n    // You can add logic here to notify the backend or update the session if needed\r\n    console.log('[ChatComponent] Local LLM model changed to:', modelId);\r\n  }\r\n\r\n  get completedSubtasks(): number {\r\n    if (!this.subtasks) return 0;\r\n    return this.subtasks.filter(subtask => !subtask.error).length;\r\n  }\r\n\r\n  retrySubtask(index: number): void {\r\n    // Simulate retry: clear error and result, set to loading, then re-run (in real app, call backend)\r\n    const subtask = this.subtasks[index];\r\n    subtask.error = null;\r\n    subtask.result = 'Retrying...';\r\n    // Simulate async retry (replace with real backend call)\r\n    setTimeout(() => {\r\n      subtask.result = 'Retried result (simulated)';\r\n      subtask.error = null;\r\n    }, 1500);\r\n  }\r\n\r\n  setSubtaskFeedback(index: number, feedback: 'up' | 'down'): void {\r\n    this.subtasks[index].feedback = feedback;\r\n  }\r\n\r\n  get longTaskInProgress(): boolean {\r\n    if (this.loading) return true;\r\n    if (this.subtasks && this.subtasks.length > 0) {\r\n      return this.subtasks.some(s => !s.result && !s.error);\r\n    }\r\n    return false;\r\n  }\r\n\r\n  toggleAutonomousMode(): void {\r\n    this.autonomousMode = !this.autonomousMode;\r\n  }\r\n\r\n  /**\r\n   * Formats a unified diff string as HTML with basic syntax highlighting for added, removed, and context lines.\r\n   * @param diff The unified diff string\r\n   * @returns HTML string\r\n   */\r\n  formatFileDiff(diff: string): string {\r\n    if (!diff) return '';\r\n    // Escape HTML\r\n    const escape = (s: string) => s.replace(/[&<>]/g, c => ({'&':'&amp;','<':'&lt;','>':'&gt;'}[c]||c));\r\n    return '<pre>' + diff.split('\\n').map(line => {\r\n      if (line.startsWith('+') && !line.startsWith('+++')) {\r\n        return `<span class='diff-added'>${escape(line)}</span>`;\r\n      } else if (line.startsWith('-') && !line.startsWith('---')) {\r\n        return `<span class='diff-removed'>${escape(line)}</span>`;\r\n      } else if (line.startsWith('@@')) {\r\n        return `<span class='diff-hunk'>${escape(line)}</span>`;\r\n      } else {\r\n        return escape(line);\r\n      }\r\n    }).join('\\n') + '</pre>';\r\n  }\r\n\r\n  /**\r\n   * Returns the current workflow stage for the progress indicator in the chat UI.\r\n   * 1 = Planning, 2 = Design, 3 = Implementation, 4 = Testing\r\n   */\r\n  get currentStage(): number {\r\n    if (!this.subtasks || this.subtasks.length === 0) return 0;\r\n    // If all subtasks are completed, return 4 (Testing)\r\n    if (this.subtasks.every(s => s.completed || s.result || s.error)) return 4;\r\n    // Otherwise, estimate stage based on subtask type or index\r\n    // (You can refine this logic as needed)\r\n    let stage = 1;\r\n    for (const subtask of this.subtasks) {\r\n      if (subtask.subtask?.type === 'design') stage = Math.max(stage, 2);\r\n      else if (subtask.subtask?.type === 'implementation') stage = Math.max(stage, 3);\r\n      else if (subtask.subtask?.type === 'testing') stage = Math.max(stage, 4);\r\n    }\r\n    return stage;\r\n  }\r\n\r\n  addReaction(messageId: string, reaction: string): void {\r\n    console.log(`[ChatComponent] Adding reaction ${reaction} to message ${messageId}`);\r\n\r\n    // Optimistically update UI\r\n    const messageToUpdate = this.messages.find(m => m.id === messageId);\r\n    if (messageToUpdate) {\r\n      if (!messageToUpdate.reactions) {\r\n        messageToUpdate.reactions = [];\r\n      }\r\n      if (!messageToUpdate.reactions.includes(reaction)) {\r\n        messageToUpdate.reactions.push(reaction);\r\n      }\r\n    }\r\n\r\n    // Send to backend\r\n    this.agentService.addMessageReaction(this.projectName, messageId, reaction).subscribe(\r\n      (response) => {\r\n        console.log('[ChatComponent] ✅ Reaction added successfully:', response);\r\n      },\r\n      (error) => {\r\n        console.error('[ChatComponent] ❌ Error adding reaction:', error);\r\n        // Remove the reaction if it failed\r\n        if (messageToUpdate && messageToUpdate.reactions) {\r\n          messageToUpdate.reactions = messageToUpdate.reactions.filter((r: string) => r !== reaction);\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  clearDebugLogs(): void {\r\n    this.debugLogs = [];\r\n  }\r\n\r\n  addDebugLog(level: string, message: string): void {\r\n    this.debugLogs.push({\r\n      timestamp: new Date(),\r\n      level: level,\r\n      message: message\r\n    });\r\n\r\n    // Keep only the last 50 logs to prevent memory issues\r\n    if (this.debugLogs.length > 50) {\r\n      this.debugLogs = this.debugLogs.slice(-50);\r\n    }\r\n  }\r\n\r\n  resetContextMemory(): void {\r\n    if (!this.projectName) return;\r\n    this.projectService.resetContextMemory(this.projectName).subscribe(\r\n      () => {\r\n        this.addDebugLog('info', 'Context memory reset successfully');\r\n        console.log('[ChatComponent] Context memory reset');\r\n      },\r\n      (error) => {\r\n        this.addDebugLog('error', `Failed to reset context memory: ${error.message}`);\r\n        console.error('[ChatComponent] Error resetting context memory:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n  toggleApiPayloads(): void {\r\n    this.showApiPayloads = !this.showApiPayloads;\r\n    this.addDebugLog('info', `API payloads visibility: ${this.showApiPayloads ? 'enabled' : 'disabled'}`);\r\n  }\r\n\r\n  toggleAgentThinking(): void {\r\n    this.showAgentThinking = !this.showAgentThinking;\r\n    this.addDebugLog('info', `Agent thinking display: ${this.showAgentThinking ? 'enabled' : 'disabled'}`);\r\n  }\r\n} ", "<div class=\"chat-container\" [ngClass]=\"{'expanded': isChatExpanded}\">\n  <div class=\"chat-header\">\n    <div class=\"agent-title\">\n      <span class=\"agent-icon\">🤖</span>\n      <h2>Autonomous AI Agent</h2>\n      <span class=\"agent-status\" [ngClass]=\"{'active': loading || longTaskInProgress || agentTyping}\">\n        {{loading || longTaskInProgress ? 'Working' : agentTyping ? 'Typing...' : 'Ready'}}\n      </span>\n    </div>\n    <div class=\"model-controls\">\n      <div class=\"model-selector cloud-model\">\n        <label for=\"modelSelect\"><i class=\"fa fa-cloud\"></i> Cloud LLM:</label>\n        <select id=\"modelSelect\" [(ngModel)]=\"selectedModel\" (change)=\"onModelChange(selectedModel)\">\n          <optgroup label=\"OpenAI\">\n            <option *ngFor=\"let model of getModelsByProvider('openai')\" [value]=\"model.id\">{{ model.name }}</option>\n          </optgroup>\n          <optgroup label=\"DeepSeek\">\n            <option *ngFor=\"let model of getModelsByProvider('deepseek')\" [value]=\"model.id\">{{ model.name }}</option>\n          </optgroup>\n          <option *ngFor=\"let model of getOtherModels()\" [value]=\"model.id\">{{ model.name }}</option>\n        </select>\n      </div>\n      <div class=\"model-selector local-model\">\n        <label for=\"localLlmSelect\"><i class=\"fa fa-desktop\"></i> Local LLM:</label>\n        <select id=\"localLlmSelect\" [(ngModel)]=\"selectedLocalLlmModel\" (change)=\"onLocalLlmModelChange(selectedLocalLlmModel)\">\n          <option *ngFor=\"let llm of localLlmModels\" [value]=\"llm.id\">{{ llm.name }}</option>\n        </select>\n      </div>\n      <div class=\"streaming-toggle\">\n        <label for=\"streamingToggle\">\n          <input type=\"checkbox\" id=\"streamingToggle\" [(ngModel)]=\"streamingEnabled\"> \n          Streaming\n        </label>\n      </div>\n    </div>\n    <div class=\"chat-actions\">\n      <button class=\"mode-toggle-btn\" [ngClass]=\"{'active': autonomousMode}\" (click)=\"toggleAutonomousMode()\" title=\"Toggle between autonomous and assisted mode\">\n        <i class=\"fa fa-robot\"></i>\n        <span>{{autonomousMode ? 'Autonomous' : 'Assisted'}}</span>\n      </button>\n      <button class=\"memory-btn\" (click)=\"resetContextMemory()\" title=\"Reset conversation memory\">\n        <i class=\"fa fa-brain\"></i>\n      </button>\n      <button class=\"api-toggle-btn\" (click)=\"toggleApiPayloads()\" [ngClass]=\"{'active': showApiPayloads}\" title=\"Toggle API payloads visibility\">\n        <i class=\"fa fa-exchange-alt\"></i>\n      </button>\n      <button class=\"clear-chat-btn\" (click)=\"clearChat()\" title=\"Clear chat history\">\n        <i class=\"fa fa-trash\"></i>\n      </button>\n      <button class=\"expand-chat-btn\" (click)=\"toggleChatExpand()\" [attr.aria-label]=\"isChatExpanded ? 'Collapse Chat' : 'Expand Chat'\">\n        <i class=\"fa\" [ngClass]=\"{'fa-chevron-up': isChatExpanded, 'fa-chevron-down': !isChatExpanded}\"></i>\n      </button>\n    </div>\n\n    <!-- Debug Log Section -->\n    <div class=\"debug-logs\" *ngIf=\"debugLogs.length > 0\">\n      <div class=\"debug-header\">\n        <span>🔍 Debug Logs</span>\n        <button (click)=\"clearDebugLogs()\" class=\"clear-logs-btn\">Clear</button>\n      </div>\n      <div class=\"debug-content\">\n        <div *ngFor=\"let log of debugLogs.slice(-10)\" class=\"debug-log-entry\" [ngClass]=\"'log-' + log.level\">\n          <span class=\"log-time\">{{log.timestamp | date:'HH:mm:ss'}}</span>\n          <span class=\"log-level\">{{log.level.toUpperCase()}}</span>\n          <span class=\"log-message\">{{log.message}}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"messages-container\" #messagesContainer>\n    <div *ngIf=\"messages.length === 0\" class=\"empty-state\">\n      <p>No messages yet. Start a conversation with the AI agent.</p>\n    </div>\n    \n    <div *ngFor=\"let message of messages | reverse\" class=\"message\" [ngClass]=\"{\n      'user-message': message.sender === 'user', \n      'agent-message': message.sender === 'agent', \n      'system-message': message.sender === 'system',\n      'browser-message': message.messageType === 'browser',\n      'openai-message': message.messageType === 'openai',\n      'llm-message': message.messageType === 'local_llm',\n      'error-message': message.messageType === 'error',\n      'api-request-message': message.messageType === 'api_request',\n      'api-response-message': message.messageType === 'api_response',\n      'streaming': message.sender === 'agent' && !message.isComplete\n      }\">\n      <div class=\"avatar\" [ngClass]=\"{\n        'user-avatar': message.sender === 'user', \n        'ai-avatar': message.sender === 'agent', \n        'system-avatar': message.sender === 'system',\n        'browser-avatar': message.messageType === 'browser',\n        'openai-avatar': message.messageType === 'openai',\n        'llm-avatar': message.messageType === 'local_llm',\n        'error-avatar': message.messageType === 'error',\n        'api-request-avatar': message.messageType === 'api_request',\n        'api-response-avatar': message.messageType === 'api_response'\n        }\">\n        <span *ngIf=\"message.sender === 'user'\">U</span>\n        <span *ngIf=\"message.sender === 'agent' && !message.messageType\">AI</span>\n        <span *ngIf=\"message.sender === 'system'\">S</span>\n        <span *ngIf=\"message.messageType === 'browser'\">🌐</span>\n        <span *ngIf=\"message.messageType === 'openai'\">🧠</span>\n        <span *ngIf=\"message.messageType === 'local_llm'\">💻</span>\n        <span *ngIf=\"message.messageType === 'error'\">⚠️</span>\n        <span *ngIf=\"message.messageType === 'system_notification'\">📣</span>\n        <span *ngIf=\"message.messageType === 'api_request'\">📤</span>\n        <span *ngIf=\"message.messageType === 'api_response'\">📥</span>\n      </div>\n      <div class=\"bubble\">\n        <div class=\"message-header\">\n          <span class=\"sender\">\n            <span *ngIf=\"message.sender === 'user'\">👤 You</span>\n            <span *ngIf=\"message.sender === 'agent' && !message.messageType\">🤖 AI Agent</span>\n            <span *ngIf=\"message.sender === 'system'\">⚙️ System</span>\n            <span *ngIf=\"message.messageType === 'browser'\">🔍 Browser</span>\n            <span *ngIf=\"message.messageType === 'openai'\">🧠 OpenAI</span>\n            <span *ngIf=\"message.messageType === 'lm_studio'\">🧠 LM Studio</span>\n            <span *ngIf=\"message.messageType === 'local_llm'\">💻 Local LLM</span>\n            <span *ngIf=\"message.messageType === 'terminal'\">💻 Terminal</span>\n            <span *ngIf=\"message.messageType === 'llm_openai'\">🧠 OpenAI</span>\n            <span *ngIf=\"message.messageType === 'llm_lm_studio'\">🧠 LM Studio</span>\n            <span *ngIf=\"message.messageType === 'error'\">⚠️ Error</span>\n            <span *ngIf=\"message.messageType === 'plan'\">📋 Plan</span>\n            <span *ngIf=\"message.messageType === 'system_notification'\">📣 Notification</span>\n            <span *ngIf=\"message.messageType === 'api_request'\">📤 API Request</span>\n            <span *ngIf=\"message.messageType === 'api_response'\">📥 API Response</span>\n          </span>\n          <span class=\"timestamp\">{{ message.timestamp | date:'short' }}</span>\n          <span class=\"message-type\" *ngIf=\"message.metadata?.modelId\">{{ message.metadata.modelId }}</span>\n          \n          <!-- Streaming indicator for incomplete messages -->\n          <span class=\"streaming-indicator\" *ngIf=\"message.sender === 'agent' && !message.isComplete\">\n            <div class=\"typing-dot\"></div>\n            <div class=\"typing-dot\"></div>\n            <div class=\"typing-dot\"></div>\n          </span>\n        </div>\n        <div class=\"message-content\" [innerHTML]=\"message.content\"></div>\n        <div class=\"message-metadata\" *ngIf=\"message.metadata && message.metadata.executionTime\">\n          <span class=\"execution-time\">Execution time: {{ message.metadata.executionTime }}ms</span>\n        </div>\n        \n        <!-- Message reactions -->\n        <div class=\"message-reactions\">\n          <div class=\"reaction-buttons\">\n            <button class=\"reaction-button\" (click)=\"addReaction(message.id, 'like')\" \n                    [class.active]=\"message.reactions && message.reactions.includes('like')\">\n              👍 <span *ngIf=\"message.reactions && message.reactions.includes('like')\">1</span>\n            </button>\n            <button class=\"reaction-button\" (click)=\"addReaction(message.id, 'dislike')\"\n                    [class.active]=\"message.reactions && message.reactions.includes('dislike')\">\n              👎 <span *ngIf=\"message.reactions && message.reactions.includes('dislike')\">1</span>\n            </button>\n            <button class=\"reaction-button\" (click)=\"addReaction(message.id, 'love')\"\n                    [class.active]=\"message.reactions && message.reactions.includes('love')\">\n              ❤️ <span *ngIf=\"message.reactions && message.reactions.includes('love')\">1</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- Typing indicator outside of messages -->\n    <div *ngIf=\"agentTyping\" class=\"typing-indicator\">\n      <div class=\"typing-dot\"></div>\n      <div class=\"typing-dot\"></div>\n      <div class=\"typing-dot\"></div>\n    </div>\n    \n    <div *ngIf=\"(loading || messagesLoading) && !agentTyping\" class=\"loading-indicator\">\n      <div class=\"spinner\"></div>\n      <p>AI Agent is thinking...</p>\n    </div>\n  </div>\n  \n  <!-- Debug/Developer Tools - Agent Thinking Display -->\n  <div *ngIf=\"showAgentThinking && agentThinkingContent\" class=\"agent-thinking-panel\">\n    <div class=\"thinking-header\">\n      <h4>Agent Thinking Process</h4>\n      <button (click)=\"toggleAgentThinking()\">Close</button>\n    </div>\n    <pre class=\"thinking-content\">{{agentThinkingContent}}</pre>\n  </div>\n  \n  <!-- Long-running task banner -->\n  <div *ngIf=\"longTaskInProgress\" class=\"long-task-banner\">\n    <span class=\"spinner\"></span>\n    <span class=\"banner-text\">The AI assistant is working on a complex, long-running task. This may take several minutes. Please do not close this window.</span>\n  </div>\n  \n  <!-- Autonomous Agent Workflow Visualization -->\n  <div *ngIf=\"subtasks && subtasks.length > 0\" class=\"autonomous-workflow\">\n    <div class=\"workflow-header\">\n      <h4>Autonomous Agent Workflow</h4>\n    </div>\n    \n    <!-- Progress Stages as in Screenshot -->\n    <div class=\"progress-stages\">\n      <div class=\"stage-icon planning\" [class.active]=\"currentStage >= 1\">\n        <span>🔍</span>\n        <div>Planning</div>\n      </div>\n      <div class=\"stage-connector\" [class.active]=\"currentStage >= 1\"></div>\n      <div class=\"stage-icon design\" [class.active]=\"currentStage >= 2\">\n        <span>🎨</span>\n        <div>Design</div>\n      </div>\n      <div class=\"stage-connector\" [class.active]=\"currentStage >= 2\"></div>\n      <div class=\"stage-icon implementation\" [class.active]=\"currentStage >= 3\">\n        <span>🛠️</span>\n        <div>Implementation</div>\n      </div>\n      <div class=\"stage-connector\" [class.active]=\"currentStage >= 3\"></div>\n      <div class=\"stage-icon testing\" [class.active]=\"currentStage >= 4\">\n        <span>🧪</span>\n        <div>Testing</div>\n      </div>\n    </div>\n    \n    <!-- Simple Progress Counter -->\n    <div class=\"progress-counter\">\n      {{completedSubtasks}}/{{subtasks.length}} steps completed\n    </div>\n    \n    <!-- Subtask Cards Grid (Exactly as in Screenshots) -->\n    <div class=\"subtasks-grid\">\n      <div *ngFor=\"let subtask of subtasks; let i = index\" class=\"subtask-card\">\n        <!-- Card Header -->\n        <div class=\"subtask-header\">\n          <div class=\"subtask-number\">{{ i + 1 }}</div>\n          <div class=\"subtask-type\">{{ subtask.subtask.type || 'COMMAND' }}</div>\n          <div class=\"model-label\" *ngIf=\"subtask.model_type === 'openai'\">GPT</div>\n          <div class=\"model-label local\" *ngIf=\"subtask.model_type === 'local'\">Local LLM</div>\n          <div class=\"hot-label\" *ngIf=\"subtask.web_research_used\">HOT</div>\n        </div>\n        \n        <!-- Task Description -->\n        <div class=\"task-description\">\n          <div class=\"bullet\">▶</div>\n          <div>{{ subtask.subtask.description || '' }}</div>\n        </div>\n        \n        <!-- Output Area -->\n        <div class=\"output-area\">\n          <div class=\"output-label\">Output:</div>\n          <div class=\"output-content\" [class.error]=\"subtask.error\">\n            {{ subtask.result || 'Processing...' }}\n          </div>\n          \n          <!-- Additional outputs if available -->\n          <div class=\"web-results\" *ngIf=\"subtask.web_results\">Web Search Results</div>\n          <div class=\"file-changes\" *ngIf=\"subtask.file_diff\">File Changes</div>\n        </div>\n        \n        <!-- Simple Feedback UI -->\n        <div class=\"feedback-row\" *ngIf=\"subtask.completed\">\n          <span>Was this helpful?</span>\n          <div class=\"feedback-options\">\n            <button [class.selected]=\"subtask.feedback === 'up'\" (click)=\"setSubtaskFeedback(i, 'up')\"></button>\n            <button [class.selected]=\"subtask.feedback === 'down'\" (click)=\"setSubtaskFeedback(i, 'down')\"></button>\n          </div>\n        </div>\n        \n        <!-- Retry Button -->\n        <button *ngIf=\"subtask.error\" class=\"retry-button\" (click)=\"retrySubtask(i)\">\n          Retry\n        </button>\n      </div>\n    </div>\n  </div>\n  \n  <!-- We've completely hidden the file changes section to avoid display issues -->\n  <!-- If you need to see file changes, you can re-enable this section later -->\n  \n  <form [formGroup]=\"messageForm\" (ngSubmit)=\"sendMessage()\" class=\"message-form\">\n    <textarea \n      formControlName=\"message\" \n      placeholder=\"Type your message here...\" \n      [disabled]=\"loading || messagesLoading || messagesSaving\"\n      rows=\"3\"\n      (keydown.enter)=\"onEnter($any($event))\"\n    ></textarea>\n    <button type=\"submit\" [disabled]=\"messageForm.invalid || loading || messagesLoading || messagesSaving\">\n      <span>Send</span>\n    </button>\n    <button class=\"export-chat-btn\" type=\"button\" (click)=\"exportChat()\" [disabled]=\"messages.length === 0 || loading || messagesSaving\" title=\"Export chat history\">Export Chat</button>\n    <button class=\"export-chat-btn\" type=\"button\" (click)=\"exportDynamicChat()\" [disabled]=\"messages.length === 0 || loading || messagesSaving\" title=\"Export chat as file (dynamic, no storage)\">Export Dynamic Chat</button>\n    <button class=\"copy-chat-btn\" type=\"button\" (click)=\"copyChat()\" [disabled]=\"messages.length === 0 || loading || messagesSaving\" title=\"Copy entire chat to clipboard\">Copy Chat</button>\n    <div *ngIf=\"messagesSaving\" class=\"saving-indicator\">Saving messages...</div>\n  </form>\n</div>\n"], "mappings": "AAAA,SAA2CA,YAAY,QAAiD,eAAe;AACvH,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;ICavDC,EAAA,CAAAC,cAAA,iBAA+E;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5CH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,EAAA,CAAkB;IAACN,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,SAAA,CAAAI,IAAA,CAAgB;;;;;IAG/FT,EAAA,CAAAC,cAAA,iBAAiF;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5CH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAJ,EAAA,CAAkB;IAACN,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAE,SAAA,CAAAD,IAAA,CAAgB;;;;;IAEnGT,EAAA,CAAAC,cAAA,iBAAkE;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5CH,EAAA,CAAAI,UAAA,UAAAO,SAAA,CAAAL,EAAA,CAAkB;IAACN,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAG,SAAA,CAAAF,IAAA,CAAgB;;;;;IAMlFT,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAxCH,EAAA,CAAAI,UAAA,UAAAQ,OAAA,CAAAN,EAAA,CAAgB;IAACN,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAQ,iBAAA,CAAAI,OAAA,CAAAH,IAAA,CAAc;;;;;IAoC5ET,EAAA,CAAAC,cAAA,cAAqG;IAC5ED,EAAA,CAAAE,MAAA,GAAmC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjEH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHoBH,EAAA,CAAAI,UAAA,qBAAAS,OAAA,CAAAC,KAAA,CAA8B;IAC3Ed,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAe,WAAA,OAAAF,OAAA,CAAAG,SAAA,cAAmC;IAClChB,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAQ,iBAAA,CAAAK,OAAA,CAAAC,KAAA,CAAAG,WAAA,GAA2B;IACzBjB,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,iBAAA,CAAAK,OAAA,CAAAK,OAAA,CAAe;;;;;;IAT/ClB,EAAA,CAAAC,cAAA,cAAqD;IAE3CD,EAAA,CAAAE,MAAA,8BAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1BH,EAAA,CAAAC,cAAA,iBAA0D;IAAlDD,EAAA,CAAAmB,UAAA,mBAAAC,sDAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAF,OAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAAwB1B,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAE1EH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA2B,UAAA,IAAAC,mCAAA,kBAIM;IACR5B,EAAA,CAAAG,YAAA,EAAM;;;;IALiBH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,YAAAyB,MAAA,CAAAC,SAAA,CAAAC,KAAA,MAAuB;;;;;IAUhD/B,EAAA,CAAAC,cAAA,cAAuD;IAClDD,EAAA,CAAAE,MAAA,+DAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IA0B7DH,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChDH,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC1EH,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAClDH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxDH,EAAA,CAAAC,cAAA,WAAkD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC3DH,EAAA,CAAAC,cAAA,WAA8C;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACvDH,EAAA,CAAAC,cAAA,WAA4D;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACrEH,EAAA,CAAAC,cAAA,WAAoD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7DH,EAAA,CAAAC,cAAA,WAAqD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAK1DH,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,uBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACrDH,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAE,MAAA,4BAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACnFH,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC1DH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,2BAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACjEH,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC/DH,EAAA,CAAAC,cAAA,WAAkD;IAAAD,EAAA,CAAAE,MAAA,6BAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACrEH,EAAA,CAAAC,cAAA,WAAkD;IAAAD,EAAA,CAAAE,MAAA,6BAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACrEH,EAAA,CAAAC,cAAA,WAAiD;IAAAD,EAAA,CAAAE,MAAA,4BAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACnEH,EAAA,CAAAC,cAAA,WAAmD;IAAAD,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACnEH,EAAA,CAAAC,cAAA,WAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzEH,EAAA,CAAAC,cAAA,WAA8C;IAAAD,EAAA,CAAAE,MAAA,yBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7DH,EAAA,CAAAC,cAAA,WAA6C;IAAAD,EAAA,CAAAE,MAAA,wBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC3DH,EAAA,CAAAC,cAAA,WAA4D;IAAAD,EAAA,CAAAE,MAAA,gCAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAClFH,EAAA,CAAAC,cAAA,WAAoD;IAAAD,EAAA,CAAAE,MAAA,+BAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzEH,EAAA,CAAAC,cAAA,WAAqD;IAAAD,EAAA,CAAAE,MAAA,gCAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAG7EH,EAAA,CAAAC,cAAA,eAA6D;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAArCH,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAQ,iBAAA,CAAAwB,WAAA,CAAAC,QAAA,CAAAC,OAAA,CAA8B;;;;;IAG3FlC,EAAA,CAAAC,cAAA,eAA4F;IAC1FD,EAAA,CAAAmC,SAAA,cAA8B;IAGhCnC,EAAA,CAAAG,YAAA,EAAO;;;;;IAGTH,EAAA,CAAAC,cAAA,cAAyF;IAC1DD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7DH,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAoC,kBAAA,qBAAAJ,WAAA,CAAAC,QAAA,CAAAI,aAAA,OAAsD;;;;;IAQ5ErC,EAAA,CAAAC,cAAA,WAAsE;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI9EH,EAAA,CAAAC,cAAA,WAAyE;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAIjFH,EAAA,CAAAC,cAAA,WAAsE;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAjF3FH,EAAA,CAAAC,cAAA,cAWK;IAYDD,EAAA,CAAA2B,UAAA,IAAAW,oCAAA,mBAAgD;IAChDtC,EAAA,CAAA2B,UAAA,IAAAY,oCAAA,mBAA0E;IAC1EvC,EAAA,CAAA2B,UAAA,IAAAa,oCAAA,mBAAkD;IAClDxC,EAAA,CAAA2B,UAAA,IAAAc,oCAAA,mBAAyD;IACzDzC,EAAA,CAAA2B,UAAA,IAAAe,oCAAA,mBAAwD;IACxD1C,EAAA,CAAA2B,UAAA,IAAAgB,oCAAA,mBAA2D;IAC3D3C,EAAA,CAAA2B,UAAA,IAAAiB,oCAAA,mBAAuD;IACvD5C,EAAA,CAAA2B,UAAA,IAAAkB,oCAAA,mBAAqE;IACrE7C,EAAA,CAAA2B,UAAA,KAAAmB,qCAAA,mBAA6D;IAC7D9C,EAAA,CAAA2B,UAAA,KAAAoB,qCAAA,mBAA8D;IAChE/C,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAoB;IAGdD,EAAA,CAAA2B,UAAA,KAAAqB,qCAAA,mBAAqD;IACrDhD,EAAA,CAAA2B,UAAA,KAAAsB,qCAAA,mBAAmF;IACnFjD,EAAA,CAAA2B,UAAA,KAAAuB,qCAAA,mBAA0D;IAC1DlD,EAAA,CAAA2B,UAAA,KAAAwB,qCAAA,mBAAiE;IACjEnD,EAAA,CAAA2B,UAAA,KAAAyB,qCAAA,mBAA+D;IAC/DpD,EAAA,CAAA2B,UAAA,KAAA0B,qCAAA,mBAAqE;IACrErD,EAAA,CAAA2B,UAAA,KAAA2B,qCAAA,mBAAqE;IACrEtD,EAAA,CAAA2B,UAAA,KAAA4B,qCAAA,mBAAmE;IACnEvD,EAAA,CAAA2B,UAAA,KAAA6B,qCAAA,mBAAmE;IACnExD,EAAA,CAAA2B,UAAA,KAAA8B,qCAAA,mBAAyE;IACzEzD,EAAA,CAAA2B,UAAA,KAAA+B,qCAAA,mBAA6D;IAC7D1D,EAAA,CAAA2B,UAAA,KAAAgC,qCAAA,mBAA2D;IAC3D3D,EAAA,CAAA2B,UAAA,KAAAiC,qCAAA,mBAAkF;IAClF5D,EAAA,CAAA2B,UAAA,KAAAkC,qCAAA,mBAAyE;IACzE7D,EAAA,CAAA2B,UAAA,KAAAmC,qCAAA,mBAA2E;IAC7E9D,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAsC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAA2B,UAAA,KAAAoC,qCAAA,mBAAkG;IAGlG/D,EAAA,CAAA2B,UAAA,KAAAqC,qCAAA,mBAIO;IACThE,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAmC,SAAA,eAAiE;IACjEnC,EAAA,CAAA2B,UAAA,KAAAsC,oCAAA,kBAEM;IAGNjE,EAAA,CAAAC,cAAA,eAA+B;IAEKD,EAAA,CAAAmB,UAAA,mBAAA+C,uDAAA;MAAA,MAAAC,WAAA,GAAAnE,EAAA,CAAAqB,aAAA,CAAA+C,IAAA;MAAA,MAAApC,WAAA,GAAAmC,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAAtE,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAA6C,OAAA,CAAAC,WAAA,CAAAvC,WAAA,CAAA1B,EAAA,EAAwB,MAAM,CAAC;IAAA,EAAC;IAEvEN,EAAA,CAAAE,MAAA,sBAAG;IAAAF,EAAA,CAAA2B,UAAA,KAAA6C,qCAAA,mBAA8E;IACnFxE,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACoF;IADpDD,EAAA,CAAAmB,UAAA,mBAAAsD,uDAAA;MAAA,MAAAN,WAAA,GAAAnE,EAAA,CAAAqB,aAAA,CAAA+C,IAAA;MAAA,MAAApC,WAAA,GAAAmC,WAAA,CAAAE,SAAA;MAAA,MAAAK,OAAA,GAAA1E,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAiD,OAAA,CAAAH,WAAA,CAAAvC,WAAA,CAAA1B,EAAA,EAAwB,SAAS,CAAC;IAAA,EAAC;IAE1EN,EAAA,CAAAE,MAAA,sBAAG;IAAAF,EAAA,CAAA2B,UAAA,KAAAgD,qCAAA,mBAAiF;IACtF3E,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACiF;IADjDD,EAAA,CAAAmB,UAAA,mBAAAyD,uDAAA;MAAA,MAAAT,WAAA,GAAAnE,EAAA,CAAAqB,aAAA,CAAA+C,IAAA;MAAA,MAAApC,WAAA,GAAAmC,WAAA,CAAAE,SAAA;MAAA,MAAAQ,OAAA,GAAA7E,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAoD,OAAA,CAAAN,WAAA,CAAAvC,WAAA,CAAA1B,EAAA,EAAwB,MAAM,CAAC;IAAA,EAAC;IAEvEN,EAAA,CAAAE,MAAA,sBAAG;IAAAF,EAAA,CAAA2B,UAAA,KAAAmD,qCAAA,mBAA8E;IACnF9E,EAAA,CAAAG,YAAA,EAAS;;;;IAlF+CH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA+E,eAAA,KAAAC,GAAA,GAAAhD,WAAA,CAAAiD,MAAA,aAAAjD,WAAA,CAAAiD,MAAA,cAAAjD,WAAA,CAAAiD,MAAA,eAAAjD,WAAA,CAAAkD,WAAA,gBAAAlD,WAAA,CAAAkD,WAAA,eAAAlD,WAAA,CAAAkD,WAAA,kBAAAlD,WAAA,CAAAkD,WAAA,cAAAlD,WAAA,CAAAkD,WAAA,oBAAAlD,WAAA,CAAAkD,WAAA,qBAAAlD,WAAA,CAAAiD,MAAA,iBAAAjD,WAAA,CAAAmD,UAAA,GAW5D;IACkBnF,EAAA,CAAAO,SAAA,GAUhB;IAVgBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA+E,eAAA,KAAAK,GAAA,GAAApD,WAAA,CAAAiD,MAAA,aAAAjD,WAAA,CAAAiD,MAAA,cAAAjD,WAAA,CAAAiD,MAAA,eAAAjD,WAAA,CAAAkD,WAAA,gBAAAlD,WAAA,CAAAkD,WAAA,eAAAlD,WAAA,CAAAkD,WAAA,kBAAAlD,WAAA,CAAAkD,WAAA,cAAAlD,WAAA,CAAAkD,WAAA,oBAAAlD,WAAA,CAAAkD,WAAA,sBAUhB;IACKlF,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAiD,MAAA,YAA+B;IAC/BjF,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAiD,MAAA,iBAAAjD,WAAA,CAAAkD,WAAA,CAAwD;IACxDlF,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAiD,MAAA,cAAiC;IACjCjF,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,eAAuC;IACvClF,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,cAAsC;IACtClF,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,iBAAyC;IACzClF,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,aAAqC;IACrClF,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,2BAAmD;IACnDlF,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,mBAA2C;IAC3ClF,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,oBAA4C;IAKxClF,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAiD,MAAA,YAA+B;IAC/BjF,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAiD,MAAA,iBAAAjD,WAAA,CAAAkD,WAAA,CAAwD;IACxDlF,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAiD,MAAA,cAAiC;IACjCjF,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,eAAuC;IACvClF,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,cAAsC;IACtClF,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,iBAAyC;IACzClF,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,iBAAyC;IACzClF,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,gBAAwC;IACxClF,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,kBAA0C;IAC1ClF,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,qBAA6C;IAC7ClF,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,aAAqC;IACrClF,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,YAAoC;IACpClF,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,2BAAmD;IACnDlF,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,mBAA2C;IAC3ClF,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAkD,WAAA,oBAA4C;IAE7BlF,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAe,WAAA,SAAAiB,WAAA,CAAAhB,SAAA,WAAsC;IAClChB,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAC,QAAA,kBAAAD,WAAA,CAAAC,QAAA,CAAAC,OAAA,CAA+B;IAGxBlC,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAiD,MAAA,iBAAAjD,WAAA,CAAAmD,UAAA,CAAuD;IAM/DnF,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAI,UAAA,cAAA4B,WAAA,CAAAqD,OAAA,EAAArF,EAAA,CAAAsF,cAAA,CAA6B;IAC3BtF,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAC,QAAA,IAAAD,WAAA,CAAAC,QAAA,CAAAI,aAAA,CAAwD;IAQ3ErC,EAAA,CAAAO,SAAA,GAAwE;IAAxEP,EAAA,CAAAuF,WAAA,WAAAvD,WAAA,CAAAwD,SAAA,IAAAxD,WAAA,CAAAwD,SAAA,CAAAC,QAAA,SAAwE;IACpEzF,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAwD,SAAA,IAAAxD,WAAA,CAAAwD,SAAA,CAAAC,QAAA,SAA6D;IAGjEzF,EAAA,CAAAO,SAAA,GAA2E;IAA3EP,EAAA,CAAAuF,WAAA,WAAAvD,WAAA,CAAAwD,SAAA,IAAAxD,WAAA,CAAAwD,SAAA,CAAAC,QAAA,YAA2E;IACvEzF,EAAA,CAAAO,SAAA,GAAgE;IAAhEP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAwD,SAAA,IAAAxD,WAAA,CAAAwD,SAAA,CAAAC,QAAA,YAAgE;IAGpEzF,EAAA,CAAAO,SAAA,GAAwE;IAAxEP,EAAA,CAAAuF,WAAA,WAAAvD,WAAA,CAAAwD,SAAA,IAAAxD,WAAA,CAAAwD,SAAA,CAAAC,QAAA,SAAwE;IACpEzF,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAI,UAAA,SAAA4B,WAAA,CAAAwD,SAAA,IAAAxD,WAAA,CAAAwD,SAAA,CAAAC,QAAA,SAA6D;;;;;IAQjFzF,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAmC,SAAA,cAA8B;IAGhCnC,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAmC,SAAA,cAA2B;IAC3BnC,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAKlCH,EAAA,CAAAC,cAAA,cAAoF;IAE5ED,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,iBAAwC;IAAhCD,EAAA,CAAAmB,UAAA,mBAAAuE,sDAAA;MAAA1F,EAAA,CAAAqB,aAAA,CAAAsE,IAAA;MAAA,MAAAC,OAAA,GAAA5F,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAmE,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAAC7F,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAExDH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA9BH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,iBAAA,CAAAsF,OAAA,CAAAC,oBAAA,CAAwB;;;;;IAIxD/F,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAmC,SAAA,eAA6B;IAC7BnC,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mIAA4H;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA4CvJH,EAAA,CAAAC,cAAA,eAAiE;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1EH,EAAA,CAAAC,cAAA,eAAsE;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACrFH,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAiBlEH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC7EH,EAAA,CAAAC,cAAA,eAAoD;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAIxEH,EAAA,CAAAC,cAAA,eAAoD;IAC5CD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9BH,EAAA,CAAAC,cAAA,eAA8B;IACyBD,EAAA,CAAAmB,UAAA,mBAAA6E,oEAAA;MAAAhG,EAAA,CAAAqB,aAAA,CAAA4E,IAAA;MAAA,MAAAC,KAAA,GAAAlG,EAAA,CAAAwB,aAAA,GAAA2E,KAAA;MAAA,MAAAC,OAAA,GAAApG,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAA2E,OAAA,CAAAC,kBAAA,CAAAH,KAAA,EAAsB,IAAI,CAAC;IAAA,EAAC;IAAClG,EAAA,CAAAG,YAAA,EAAS;IACpGH,EAAA,CAAAC,cAAA,iBAA+F;IAAxCD,EAAA,CAAAmB,UAAA,mBAAAmF,oEAAA;MAAAtG,EAAA,CAAAqB,aAAA,CAAA4E,IAAA;MAAA,MAAAC,KAAA,GAAAlG,EAAA,CAAAwB,aAAA,GAAA2E,KAAA;MAAA,MAAAI,OAAA,GAAAvG,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAA8E,OAAA,CAAAF,kBAAA,CAAAH,KAAA,EAAsB,MAAM,CAAC;IAAA,EAAC;IAAClG,EAAA,CAAAG,YAAA,EAAS;;;;IADhGH,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAuF,WAAA,aAAAiB,WAAA,CAAAC,QAAA,UAA4C;IAC5CzG,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAuF,WAAA,aAAAiB,WAAA,CAAAC,QAAA,YAA8C;;;;;;IAK1DzG,EAAA,CAAAC,cAAA,kBAA6E;IAA1BD,EAAA,CAAAmB,UAAA,mBAAAuF,uEAAA;MAAA1G,EAAA,CAAAqB,aAAA,CAAAsF,IAAA;MAAA,MAAAT,KAAA,GAAAlG,EAAA,CAAAwB,aAAA,GAAA2E,KAAA;MAAA,MAAAS,OAAA,GAAA5G,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAmF,OAAA,CAAAC,YAAA,CAAAX,KAAA,CAAe;IAAA,EAAC;IAC1ElG,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAxCXH,EAAA,CAAAC,cAAA,cAA0E;IAG1CD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7CH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvEH,EAAA,CAAA2B,UAAA,IAAAmF,0CAAA,mBAA0E;IAC1E9G,EAAA,CAAA2B,UAAA,IAAAoF,0CAAA,mBAAqF;IACrF/G,EAAA,CAAA2B,UAAA,IAAAqF,0CAAA,mBAAkE;IACpEhH,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA8B;IACRD,EAAA,CAAAE,MAAA,cAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3BH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIpDH,EAAA,CAAAC,cAAA,gBAAyB;IACGD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvCH,EAAA,CAAAC,cAAA,gBAA0D;IACxDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA2B,UAAA,KAAAsF,2CAAA,mBAA6E;IAC7EjH,EAAA,CAAA2B,UAAA,KAAAuF,2CAAA,mBAAsE;IACxElH,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA2B,UAAA,KAAAwF,2CAAA,mBAMM;IAGNnH,EAAA,CAAA2B,UAAA,KAAAyF,8CAAA,sBAES;IACXpH,EAAA,CAAAG,YAAA,EAAM;;;;;IAtC0BH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAQ,iBAAA,CAAA0F,KAAA,KAAW;IACblG,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,iBAAA,CAAAgG,WAAA,CAAAa,OAAA,CAAAC,IAAA,cAAuC;IACvCtH,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAAoG,WAAA,CAAAe,UAAA,cAAqC;IAC/BvH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAI,UAAA,SAAAoG,WAAA,CAAAe,UAAA,aAAoC;IAC5CvH,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAAoG,WAAA,CAAAgB,iBAAA,CAA+B;IAMlDxH,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,iBAAA,CAAAgG,WAAA,CAAAa,OAAA,CAAAI,WAAA,OAAuC;IAMhBzH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAuF,WAAA,UAAAiB,WAAA,CAAAkB,KAAA,CAA6B;IACvD1H,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAoC,kBAAA,MAAAoE,WAAA,CAAAmB,MAAA,yBACF;IAG0B3H,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAoG,WAAA,CAAAoB,WAAA,CAAyB;IACxB5H,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAoG,WAAA,CAAAqB,SAAA,CAAuB;IAIzB7H,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAoG,WAAA,CAAAsB,SAAA,CAAuB;IASzC9H,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAoG,WAAA,CAAAkB,KAAA,CAAmB;;;;;IAzElC1H,EAAA,CAAAC,cAAA,cAAyE;IAEjED,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIpCH,EAAA,CAAAC,cAAA,cAA6B;IAEnBD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACfH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAErBH,EAAA,CAAAmC,SAAA,eAAsE;IACtEnC,EAAA,CAAAC,cAAA,eAAkE;IAC1DD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACfH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEnBH,EAAA,CAAAmC,SAAA,eAAsE;IACtEnC,EAAA,CAAAC,cAAA,eAA0E;IAClED,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChBH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE3BH,EAAA,CAAAmC,SAAA,eAAsE;IACtEnC,EAAA,CAAAC,cAAA,eAAmE;IAC3DD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACfH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAKtBH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAA2B,UAAA,KAAAoG,oCAAA,oBAyCM;IACR/H,EAAA,CAAAG,YAAA,EAAM;;;;IAtE6BH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAuF,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAItCjI,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAuF,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAChCjI,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAuF,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAIpCjI,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAuF,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IACxBjI,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAuF,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAI5CjI,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAuF,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAC/BjI,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAuF,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAQlEjI,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAkI,kBAAA,MAAAF,OAAA,CAAAG,iBAAA,OAAAH,OAAA,CAAAI,QAAA,CAAAC,MAAA,sBACF;IAI2BrI,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAI,UAAA,YAAA4H,OAAA,CAAAI,QAAA,CAAa;;;;;IA8DxCpI,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;;;;;;;;;AD3QjF,OAAM,MAAOmI,aAAa;EA8BxBC,YACUC,EAAe,EACfC,aAA4B,EAC5BC,cAA8B,EAC9BC,YAA0B;IAH1B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IAjCb,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,cAAc,GAAY,KAAK;IAC9B,KAAAC,YAAY,GAAG,IAAIjJ,YAAY,EAAO;IACtC,KAAAkJ,gBAAgB,GAAG,IAAIlJ,YAAY,EAAW;IAIxD,KAAAmJ,QAAQ,GAAU,EAAE;IACpB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,aAAa,GAAW,yBAAyB;IACjD,KAAAC,cAAc,GAAU,CACtB;MAAE/I,EAAE,EAAE,4BAA4B;MAAEG,IAAI,EAAE;IAA4B;IACtE;IAAA,CACD;;IACD,KAAA6I,qBAAqB,GAAW,4BAA4B;IAC5D,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAnB,QAAQ,GAAU,EAAE;IACpB,KAAAoB,cAAc,GAAY,KAAK;IAC/B,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAA1D,oBAAoB,GAAW,EAAE;IACjC,KAAA2D,iBAAiB,GAAY,KAAK;IAClC,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,eAAe,GAAY,IAAI;IAC/B,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAhI,SAAS,GAAU,EAAE;EAOlB;EAEHiI,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAA,cAAcA,CAAA;IACZ,IAAI;MACF,IAAI,IAAI,CAACC,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACC,aAAa,EAAE;QAClE,IAAI,CAACD,iBAAiB,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAACF,iBAAiB,CAACC,aAAa,CAACE,YAAY;;KAErG,CAAC,OAAOC,GAAG,EAAE;EAChB;EAEAV,QAAQA,CAAA;IACNF,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,IAAI,CAACY,WAAW,GAAG,IAAI,CAACrC,EAAE,CAACsC,KAAK,CAAC;MAC/B5J,OAAO,EAAE,CAAC,EAAE,EAAEnB,UAAU,CAACgL,QAAQ;KAClC,CAAC;EACJ;EAEAZ,YAAYA,CAAA;IACVH,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE,IAAI,CAACrB,WAAW,CAAC;IACjF,IAAI,CAAC,IAAI,CAACA,WAAW,EAAE;MACrBoB,OAAO,CAACgB,IAAI,CAAC,8DAA8D,CAAC;MAC5E;;IAGF,IAAI,CAAC,IAAI,CAACnC,eAAe,EAAE;MACzB,IAAI,CAACK,OAAO,GAAG,IAAI;;IAGrB,IAAI,CAACR,cAAc,CAACuC,kBAAkB,CAAC,IAAI,CAACrC,WAAW,CAAC,CAACsC,SAAS,CAC/DC,QAAa,IAAI;MAChBnB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEkB,QAAQ,CAAC;MACjE,IAAI,CAAClC,QAAQ,GAAGkC,QAAQ,CAAClC,QAAQ,IAAI,EAAE;MACvC,IAAI,CAACC,OAAO,GAAG,KAAK;IACtB,CAAC,EACAxB,KAAU,IAAI;MACbsC,OAAO,CAACtC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,IAAI,CAACwB,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEAkB,UAAUA,CAAA;IACRJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,IAAI,CAACtB,YAAY,CAACyC,SAAS,EAAE,CAACF,SAAS,CACpCC,QAAa,IAAI;MAChBnB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkB,QAAQ,CAAC;MACvD,IAAI,CAAChC,MAAM,GAAGgC,QAAQ,CAAChC,MAAM,IAAI,EAAE;IACrC,CAAC,EACAzB,KAAU,IAAI;MACbsC,OAAO,CAACtC,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE,CAAC,CACF;EACH;EAEA;;;EAGA2D,mBAAmBA,CAACC,QAAgB;IAClC,OAAO,IAAI,CAACnC,MAAM,CAACoC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAClL,EAAE,CAACmL,UAAU,CAAC,GAAGH,QAAQ,GAAG,CAAC,CAAC;EACzE;EAEA;;;EAGAI,cAAcA,CAAA;IACZ,MAAMC,cAAc,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC;IAC7C,OAAO,IAAI,CAACxC,MAAM,CAACoC,MAAM,CAACC,KAAK,IAC7B,CAACG,cAAc,CAACC,IAAI,CAACN,QAAQ,IAAIE,KAAK,CAAClL,EAAE,CAACmL,UAAU,CAAC,GAAGH,QAAQ,GAAG,CAAC,CAAC,CACtE;EACH;EAEAjB,oBAAoBA,CAAA;IAClBL,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAE1D,IAAI,CAACxB,aAAa,CAACoD,EAAE,CAAC,eAAe,CAAC,CAACX,SAAS,CAAEY,IAAiB,IAAI;MACrE9B,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE6B,IAAI,CAAC;MACxE,IAAI,CAACC,WAAW,CAAC,MAAM,EAAE,2BAA2BD,IAAI,CAAC5K,OAAO,EAAE8K,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;MAExF,IAAIF,IAAI,CAACG,YAAY,KAAK,IAAI,CAACrD,WAAW,EAAE;QAC1C,IAAI,CAACK,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACsC,MAAM,CAACW,CAAC,IAAI,CAACA,CAAC,CAACC,yBAAyB,CAAC;QAEvE;QACA,MAAMjH,WAAW,GAAG4G,IAAI,CAACM,YAAY,IAAI,OAAO;QAEhD,IAAI,CAACnD,QAAQ,CAACoD,IAAI,CAAC;UACjB/L,EAAE,EAAEwL,IAAI,CAACQ,UAAU,IAAI,OAAOC,IAAI,CAACC,GAAG,EAAE,EAAE;UAC1CvH,MAAM,EAAE,OAAO;UACfI,OAAO,EAAEyG,IAAI,CAAC5K,OAAO;UACrBF,SAAS,EAAE,IAAIuL,IAAI,EAAE;UACrBJ,yBAAyB,EAAE,KAAK;UAChCjH,WAAW,EAAEA,WAAW;UACxBM,SAAS,EAAE;SACZ,CAAC;QACF,IAAI,CAAC0D,OAAO,GAAG,KAAK;QACpBc,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;IAEnE,CAAC,CAAC;IAEF,IAAI,CAACxB,aAAa,CAACoD,EAAE,CAAC,cAAc,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MAC5D9B,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE6B,IAAI,CAAC;MAEvE,IAAIA,IAAI,CAACG,YAAY,KAAK,IAAI,CAACrD,WAAW,EAAE;QAC1C,IAAI,CAACa,WAAW,GAAGqC,IAAI,CAACW,SAAS;QACjC;QACA,IAAI,CAACX,IAAI,CAACW,SAAS,EAAE;UACnB,IAAI,CAACxD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACsC,MAAM,CAACW,CAAC,IAAI,CAACA,CAAC,CAACC,yBAAyB,CAAC;;;IAG7E,CAAC,CAAC;IAEF,IAAI,CAAC1D,aAAa,CAACoD,EAAE,CAAC,oBAAoB,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MAClE9B,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE6B,IAAI,CAAC;MAE7E,IAAIA,IAAI,CAACG,YAAY,KAAK,IAAI,CAACrD,WAAW,EAAE;QAC1C;QACA,IAAI8D,gBAAgB,GAAG,IAAI,CAACzD,QAAQ,CAAC0D,IAAI,CAACT,CAAC,IAAIA,CAAC,CAACjH,MAAM,KAAK,OAAO,IAAI,CAACiH,CAAC,CAAC/G,UAAU,CAAC;QAErF,IAAI,CAACuH,gBAAgB,EAAE;UACrBA,gBAAgB,GAAG;YACjBpM,EAAE,EAAE,UAAUiM,IAAI,CAACC,GAAG,EAAE,EAAE;YAC1BvH,MAAM,EAAE,OAAO;YACfI,OAAO,EAAE,EAAE;YACXrE,SAAS,EAAE,IAAIuL,IAAI,EAAE;YACrBpH,UAAU,EAAE,KAAK;YACjBK,SAAS,EAAE;WACZ;UACD,IAAI,CAACyD,QAAQ,CAACoD,IAAI,CAACK,gBAAgB,CAAC;;QAGtC;QACAA,gBAAgB,CAACrH,OAAO,IAAIyG,IAAI,CAACc,KAAK;QACtC,IAAI,CAACrC,cAAc,EAAE;;IAEzB,CAAC,CAAC;IAEF,IAAI,CAAC9B,aAAa,CAACoD,EAAE,CAAC,gBAAgB,CAAC,CAACX,SAAS,CAAEY,IAAkB,IAAI;MACvE9B,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE6B,IAAI,CAAC;MAExE,IAAIA,IAAI,CAACG,YAAY,KAAK,IAAI,CAACrD,WAAW,EAAE;QAC1C,IAAI,CAACM,OAAO,GAAG,KAAK;QACpB,IAAI,CAACO,WAAW,GAAG,KAAK;QAExB;QACA,IAAI,CAACR,QAAQ,CAAC4D,OAAO,CAAC3L,OAAO,IAAG;UAC9B,IAAIA,OAAO,CAAC+D,MAAM,KAAK,OAAO,EAAE;YAC9B/D,OAAO,CAACiE,UAAU,GAAG,IAAI;;QAE7B,CAAC,CAAC;QAEF;QACA,IAAI,CAAC8D,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACsC,MAAM,CAACW,CAAC,IAAI,CAACA,CAAC,CAACC,yBAAyB,CAAC;QACvEnC,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;;IAE7E,CAAC,CAAC;IAEF,IAAI,CAACxB,aAAa,CAACoD,EAAE,CAAC,gBAAgB,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MAC9D9B,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE6B,IAAI,CAAC;MAEzE,IAAIA,IAAI,CAACG,YAAY,KAAK,IAAI,CAACrD,WAAW,IAAI,IAAI,CAACc,iBAAiB,EAAE;QACpE;QACA,IAAI,CAAC3D,oBAAoB,GAAG+F,IAAI,CAACgB,QAAQ;;IAE7C,CAAC,CAAC;IAEF,IAAI,CAACrE,aAAa,CAACoD,EAAE,CAAC,kBAAkB,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MAChE9B,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE6B,IAAI,CAAC;MAE3E,IAAIA,IAAI,CAACG,YAAY,KAAK,IAAI,CAACrD,WAAW,EAAE;QAC1C;QACA,MAAMmE,eAAe,GAAG,IAAI,CAAC9D,QAAQ,CAAC0D,IAAI,CAACT,CAAC,IAAIA,CAAC,CAAC5L,EAAE,KAAKwL,IAAI,CAACQ,UAAU,CAAC;QACzE,IAAIS,eAAe,EAAE;UACnB,IAAI,CAACA,eAAe,CAACvH,SAAS,EAAE;YAC9BuH,eAAe,CAACvH,SAAS,GAAG,EAAE;;UAEhC,IAAI,CAACuH,eAAe,CAACvH,SAAS,CAACC,QAAQ,CAACqG,IAAI,CAACkB,QAAQ,CAAC,EAAE;YACtDD,eAAe,CAACvH,SAAS,CAAC6G,IAAI,CAACP,IAAI,CAACkB,QAAQ,CAAC;;;;IAIrD,CAAC,CAAC;IAEF;IACA,IAAI,CAACvE,aAAa,CAACoD,EAAE,CAAC,OAAO,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MACrD9B,OAAO,CAACtC,KAAK,CAAC,iCAAiC,EAAEoE,IAAI,CAAC;MACtD,IAAI,CAACC,WAAW,CAAC,OAAO,EAAE,iBAAiBkB,IAAI,CAACC,SAAS,CAACpB,IAAI,CAAC,EAAE,CAAC;IACpE,CAAC,CAAC;IAEF;IACA,IAAI,CAACrD,aAAa,CAACoD,EAAE,CAAC,aAAa,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MAC3D9B,OAAO,CAACtC,KAAK,CAAC,gCAAgC,EAAEoE,IAAI,CAAC;MACrD,IAAI,CAACC,WAAW,CAAC,OAAO,EAAE,gBAAgBD,IAAI,CAACpE,KAAK,IAAI,eAAe,EAAE,CAAC;MAE1E,IAAIoE,IAAI,CAACG,YAAY,KAAK,IAAI,CAACrD,WAAW,EAAE;QAC1C,IAAI,CAACM,OAAO,GAAG,KAAK;QACpB,IAAI,CAACO,WAAW,GAAG,KAAK;QAExB;QACA,IAAI,CAACR,QAAQ,CAACoD,IAAI,CAAC;UACjB/L,EAAE,EAAE,SAASiM,IAAI,CAACC,GAAG,EAAE,EAAE;UACzBvH,MAAM,EAAE,QAAQ;UAChBC,WAAW,EAAE,OAAO;UACpBG,OAAO,EAAE,oCAAoCyG,IAAI,CAACpE,KAAK,IAAI,wBAAwB,EAAE;UACrF1G,SAAS,EAAE,IAAIuL,IAAI,EAAE;UACrB/G,SAAS,EAAE;SACZ,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA2H,WAAWA,CAAA;IACT,IAAI,IAAI,CAACtC,WAAW,CAACuC,OAAO,EAAE;MAC5B;;IAGF,MAAMC,cAAc,GAAG,IAAI,CAACxC,WAAW,CAACyC,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK;IAC7D,IAAI,CAACF,cAAc,IAAI,CAAC,IAAI,CAACzE,WAAW,EAAE;MACxC;;IAGF;IACA,MAAM4E,aAAa,GAAG,OAAOjB,IAAI,CAACC,GAAG,EAAE,EAAE;IACzC,IAAI,CAACvD,QAAQ,CAACoD,IAAI,CAAC;MACjB/L,EAAE,EAAEkN,aAAa;MACjBvI,MAAM,EAAE,MAAM;MACdI,OAAO,EAAEgI,cAAc;MACvBrM,SAAS,EAAE,IAAIuL,IAAI,EAAE;MACrB/G,SAAS,EAAE;KACZ,CAAC;IAEF;IACA,MAAMiI,cAAc,GAAG;MACrBxB,YAAY,EAAE,IAAI,CAACrD,WAAW;MAC9B1H,OAAO,EAAEmM,cAAc;MACvBK,QAAQ,EAAE,IAAI,CAACtE,aAAa;MAC5BuE,kBAAkB,EAAE,IAAI,CAACrE,qBAAqB;MAC9CsE,iBAAiB,EAAE,IAAI,CAACjE;KACzB;IAED;IACA,MAAMkE,YAAY,GAAG;MACnB7M,SAAS,EAAE,IAAIuL,IAAI,EAAE;MACrBjF,IAAI,EAAE,SAAS;MACfwG,QAAQ,EAAE,aAAa,IAAI,CAAClF,WAAW,WAAW;MAClDmF,OAAO,EAAEN;KACV;IACD,IAAI,CAAC5D,WAAW,CAACwC,IAAI,CAACwB,YAAY,CAAC;IAEnC;IACA,IAAI,IAAI,CAACjE,eAAe,EAAE;MACxB,IAAI,CAACX,QAAQ,CAACoD,IAAI,CAAC;QACjB/L,EAAE,EAAE,WAAWiM,IAAI,CAACC,GAAG,EAAE,EAAE;QAC3BvH,MAAM,EAAE,QAAQ;QAChBC,WAAW,EAAE,aAAa;QAC1BG,OAAO,EAAE,yCAAyC4H,IAAI,CAACC,SAAS,CAACO,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ;QACjGzM,SAAS,EAAE,IAAIuL,IAAI,EAAE;QACrB/G,SAAS,EAAE;OACZ,CAAC;;IAGJ;IACA,IAAI,CAACqF,WAAW,CAACmD,KAAK,EAAE;IAExB;IACA,IAAI,CAAC9E,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,CAAC6C,WAAW,CAAC,MAAM,EAAE,+BAA+B,IAAI,CAAC3C,aAAa,EAAE,CAAC;IAC7E,IAAI,IAAI,CAACO,gBAAgB,EAAE;MACzB;MACA,IAAI,CAACoC,WAAW,CAAC,MAAM,EAAE,oCAAoC,CAAC;MAC9D,IAAI,CAACtD,aAAa,CAAC0E,WAAW,CAAC,IAAI,CAACvE,WAAW,EAAEyE,cAAc,EAAE,IAAI,CAACjE,aAAa,CAAC;KACrF,MAAM;MACL;MACA,IAAI,CAACT,YAAY,CAACwE,WAAW,CAC3B,IAAI,CAACvE,WAAW,EAChByE,cAAc,EACd,IAAI,CAACjE,aAAa,EAClB,IAAI,CAACE,qBAAqB,EAC1B,KAAK,CACN,CAAC4B,SAAS,CACRC,QAAa,IAAI;QAChB;QACA,MAAM8C,aAAa,GAAG;UACpBjN,SAAS,EAAE,IAAIuL,IAAI,EAAE;UACrBjF,IAAI,EAAE,UAAU;UAChBwG,QAAQ,EAAE,aAAa,IAAI,CAAClF,WAAW,WAAW;UAClDmF,OAAO,EAAE5C;SACV;QACD,IAAI,CAACrB,YAAY,CAACuC,IAAI,CAAC4B,aAAa,CAAC;QAErC;QACA,IAAI,IAAI,CAACrE,eAAe,EAAE;UACxB,IAAI,CAACX,QAAQ,CAACoD,IAAI,CAAC;YACjB/L,EAAE,EAAE,WAAWiM,IAAI,CAACC,GAAG,EAAE,EAAE;YAC3BvH,MAAM,EAAE,QAAQ;YAChBC,WAAW,EAAE,cAAc;YAC3BG,OAAO,EAAE,0CAA0C4H,IAAI,CAACC,SAAS,CAAC/B,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ;YAC5FnK,SAAS,EAAE,IAAIuL,IAAI,EAAE;YACrB/G,SAAS,EAAE;WACZ,CAAC;;QAGJ,IAAI,CAAC0D,OAAO,GAAG,KAAK;MACtB,CAAC,EACAxB,KAAU,IAAI;QACbsC,OAAO,CAACtC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAEhE;QACA,MAAMwG,UAAU,GAAG;UACjBlN,SAAS,EAAE,IAAIuL,IAAI,EAAE;UACrBjF,IAAI,EAAE,OAAO;UACbwG,QAAQ,EAAE,aAAa,IAAI,CAAClF,WAAW,WAAW;UAClDmF,OAAO,EAAErG;SACV;QACD,IAAI,CAACoC,YAAY,CAACuC,IAAI,CAAC6B,UAAU,CAAC;QAElC;QACA,IAAI,CAACjF,QAAQ,CAACoD,IAAI,CAAC;UACjB/L,EAAE,EAAE,SAASiM,IAAI,CAACC,GAAG,EAAE,EAAE;UACzBvH,MAAM,EAAE,QAAQ;UAChBC,WAAW,EAAE,OAAO;UACpBG,OAAO,EAAE,uCAAuC4H,IAAI,CAACC,SAAS,CAACxF,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ;UACtF1G,SAAS,EAAE,IAAIuL,IAAI,EAAE;UACrB/G,SAAS,EAAE;SACZ,CAAC;QAEF,IAAI,CAAC0D,OAAO,GAAG,KAAK;MACtB,CAAC,CACF;;EAEL;EAEAiF,aAAaA,CAACjM,OAAe;IAC3B8H,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE/H,OAAO,CAAC;IACzD,IAAI,CAACkH,aAAa,GAAGlH,OAAO;EAC9B;EAEAkM,aAAaA,CAACjI,KAAa;IACzB,IAAI,CAAC8C,QAAQ,CAACoF,MAAM,CAAClI,KAAK,EAAE,CAAC,CAAC;EAChC;EAEAmI,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAAC1F,WAAW,EAAE;IACvB;IACA,IAAI,CAACK,QAAQ,GAAG,EAAE;IAClB;IACA,IAAI,CAACP,cAAc,CAAC6F,qBAAqB,CAAC,IAAI,CAAC3F,WAAW,CAAC,CAACsC,SAAS,CACnE,MAAK;MACHlB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAChE,CAAC,EACAvC,KAAK,IAAI;MACRsC,OAAO,CAACtC,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;IACtE,CAAC,CACF;EACH;EAEA8G,gBAAgBA,CAAA;IACd,IAAI,CAACjF,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,CAACP,gBAAgB,CAACyF,IAAI,CAAC,IAAI,CAAClF,cAAc,CAAC;EACjD;EAEAmF,OAAOA,CAACC,KAAoB;IAC1B,IAAIA,KAAK,CAACC,QAAQ,EAAE;MAClB,OAAO,CAAC;;;IAEVD,KAAK,CAACE,cAAc,EAAE;IACtB,IAAI,CAAC1B,WAAW,EAAE;EACpB;EAEA2B,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAClG,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,CAACqG,iBAAiB,CAAC,IAAI,CAACnG,WAAW,CAAC,CAACsC,SAAS,CAC9DC,QAAa,IAAI;MAChB6D,KAAK,CAAC,iBAAiB,IAAI7D,QAAQ,EAAE8D,SAAS,IAAI,SAAS,CAAC,CAAC;IAC/D,CAAC,EACAvH,KAAU,IAAI;MACbsH,KAAK,CAAC,yBAAyB,IAAItH,KAAK,EAAEA,KAAK,EAAEwH,MAAM,IAAIxH,KAAK,CAAC,CAAC;IACpE,CAAC,CACF;EACH;EAEAyH,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAClG,QAAQ,CAACZ,MAAM,EAAE;IAC3B;IACA,MAAM+G,QAAQ,GAAG,IAAI,CAACnG,QAAQ,CAACoG,GAAG,CAACnD,CAAC,IAAI,IAAIA,CAAC,CAACjH,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,KAAKiH,CAAC,CAAC7G,OAAO,EAAE,CAAC,CAACiK,IAAI,CAAC,IAAI,CAAC;IAC1G,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,QAAQ,CAAC,EAAE;MAAE9H,IAAI,EAAE;IAAY,CAAE,CAAC;IACzD,MAAMmI,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAC5C,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrC,MAAM/O,SAAS,GAAG,IAAIuL,IAAI,EAAE,CAACyD,WAAW,EAAE,CAACC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;IAChE,MAAMC,QAAQ,GAAG,GAAG,IAAI,CAACtH,WAAW,IAAI,MAAM,YAAY5H,SAAS,MAAM;IACzE6O,CAAC,CAACM,IAAI,GAAGV,GAAG;IACZI,CAAC,CAACO,QAAQ,GAAGF,QAAQ;IACrBJ,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,CAAC,CAAC;IAC5BA,CAAC,CAACU,KAAK,EAAE;IACTC,UAAU,CAAC,MAAK;MACdV,QAAQ,CAACO,IAAI,CAACI,WAAW,CAACZ,CAAC,CAAC;MAC5BH,MAAM,CAACC,GAAG,CAACe,eAAe,CAACjB,GAAG,CAAC;IACjC,CAAC,EAAE,CAAC,CAAC;EACP;EAEAkB,QAAQA,CAAA;IACN,MAAMvB,QAAQ,GAAG,IAAI,CAACnG,QAAQ,CAACoG,GAAG,CAACnD,CAAC,IAAI,IAAIA,CAAC,CAACjH,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,KAAKiH,CAAC,CAAC7G,OAAO,EAAE,CAAC,CAACiK,IAAI,CAAC,IAAI,CAAC;IAC1GsB,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC1B,QAAQ,CAAC,CAAC2B,IAAI,CAC1C,MAAM/B,KAAK,CAAC,2BAA2B,CAAC,EACxC,MAAMA,KAAK,CAAC,mCAAmC,CAAC,CACjD;EACH;EAEAgC,qBAAqBA,CAAC9O,OAAe;IACnC,IAAI,CAACoH,qBAAqB,GAAGpH,OAAO;IACpC;IACA8H,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE/H,OAAO,CAAC;EACrE;EAEA,IAAIiG,iBAAiBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE,OAAO,CAAC;IAC5B,OAAO,IAAI,CAACA,QAAQ,CAACmD,MAAM,CAAClE,OAAO,IAAI,CAACA,OAAO,CAACK,KAAK,CAAC,CAACW,MAAM;EAC/D;EAEAxB,YAAYA,CAACV,KAAa;IACxB;IACA,MAAMkB,OAAO,GAAG,IAAI,CAACe,QAAQ,CAACjC,KAAK,CAAC;IACpCkB,OAAO,CAACK,KAAK,GAAG,IAAI;IACpBL,OAAO,CAACM,MAAM,GAAG,aAAa;IAC9B;IACA6I,UAAU,CAAC,MAAK;MACdnJ,OAAO,CAACM,MAAM,GAAG,4BAA4B;MAC7CN,OAAO,CAACK,KAAK,GAAG,IAAI;IACtB,CAAC,EAAE,IAAI,CAAC;EACV;EAEArB,kBAAkBA,CAACF,KAAa,EAAEM,QAAuB;IACvD,IAAI,CAAC2B,QAAQ,CAACjC,KAAK,CAAC,CAACM,QAAQ,GAAGA,QAAQ;EAC1C;EAEA,IAAIwK,kBAAkBA,CAAA;IACpB,IAAI,IAAI,CAAC/H,OAAO,EAAE,OAAO,IAAI;IAC7B,IAAI,IAAI,CAACd,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MAC7C,OAAO,IAAI,CAACD,QAAQ,CAACwD,IAAI,CAACsF,CAAC,IAAI,CAACA,CAAC,CAACvJ,MAAM,IAAI,CAACuJ,CAAC,CAACxJ,KAAK,CAAC;;IAEvD,OAAO,KAAK;EACd;EAEAyJ,oBAAoBA,CAAA;IAClB,IAAI,CAAC3H,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEA;;;;;EAKA4H,cAAcA,CAACC,IAAY;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB;IACA,MAAMC,MAAM,GAAIJ,CAAS,IAAKA,CAAC,CAACjB,OAAO,CAAC,QAAQ,EAAEsB,CAAC,IAAK;MAAC,GAAG,EAAC,OAAO;MAAC,GAAG,EAAC,MAAM;MAAC,GAAG,EAAC;IAAM,CAAC,EAACA,CAAC,CAAC,IAAEA,CAAE,CAAC;IACnG,OAAO,OAAO,GAAGF,IAAI,CAACG,KAAK,CAAC,IAAI,CAAC,CAACnC,GAAG,CAACoC,IAAI,IAAG;MAC3C,IAAIA,IAAI,CAAChG,UAAU,CAAC,GAAG,CAAC,IAAI,CAACgG,IAAI,CAAChG,UAAU,CAAC,KAAK,CAAC,EAAE;QACnD,OAAO,4BAA4B6F,MAAM,CAACG,IAAI,CAAC,SAAS;OACzD,MAAM,IAAIA,IAAI,CAAChG,UAAU,CAAC,GAAG,CAAC,IAAI,CAACgG,IAAI,CAAChG,UAAU,CAAC,KAAK,CAAC,EAAE;QAC1D,OAAO,8BAA8B6F,MAAM,CAACG,IAAI,CAAC,SAAS;OAC3D,MAAM,IAAIA,IAAI,CAAChG,UAAU,CAAC,IAAI,CAAC,EAAE;QAChC,OAAO,2BAA2B6F,MAAM,CAACG,IAAI,CAAC,SAAS;OACxD,MAAM;QACL,OAAOH,MAAM,CAACG,IAAI,CAAC;;IAEvB,CAAC,CAAC,CAACnC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ;EAC1B;EAEA;;;;EAIA,IAAIrH,YAAYA,CAAA;IACd,IAAI,CAAC,IAAI,CAACG,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC1D;IACA,IAAI,IAAI,CAACD,QAAQ,CAACsJ,KAAK,CAACR,CAAC,IAAIA,CAAC,CAACpJ,SAAS,IAAIoJ,CAAC,CAACvJ,MAAM,IAAIuJ,CAAC,CAACxJ,KAAK,CAAC,EAAE,OAAO,CAAC;IAC1E;IACA;IACA,IAAIiK,KAAK,GAAG,CAAC;IACb,KAAK,MAAMtK,OAAO,IAAI,IAAI,CAACe,QAAQ,EAAE;MACnC,IAAIf,OAAO,CAACA,OAAO,EAAEC,IAAI,KAAK,QAAQ,EAAEqK,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC,KAC9D,IAAItK,OAAO,CAACA,OAAO,EAAEC,IAAI,KAAK,gBAAgB,EAAEqK,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC,KAC3E,IAAItK,OAAO,CAACA,OAAO,EAAEC,IAAI,KAAK,SAAS,EAAEqK,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,KAAK,EAAE,CAAC,CAAC;;IAE1E,OAAOA,KAAK;EACd;EAEApN,WAAWA,CAACuN,SAAiB,EAAE9E,QAAgB;IAC7ChD,OAAO,CAACC,GAAG,CAAC,mCAAmC+C,QAAQ,eAAe8E,SAAS,EAAE,CAAC;IAElF;IACA,MAAM/E,eAAe,GAAG,IAAI,CAAC9D,QAAQ,CAAC0D,IAAI,CAACT,CAAC,IAAIA,CAAC,CAAC5L,EAAE,KAAKwR,SAAS,CAAC;IACnE,IAAI/E,eAAe,EAAE;MACnB,IAAI,CAACA,eAAe,CAACvH,SAAS,EAAE;QAC9BuH,eAAe,CAACvH,SAAS,GAAG,EAAE;;MAEhC,IAAI,CAACuH,eAAe,CAACvH,SAAS,CAACC,QAAQ,CAACuH,QAAQ,CAAC,EAAE;QACjDD,eAAe,CAACvH,SAAS,CAAC6G,IAAI,CAACW,QAAQ,CAAC;;;IAI5C;IACA,IAAI,CAACrE,YAAY,CAACoJ,kBAAkB,CAAC,IAAI,CAACnJ,WAAW,EAAEkJ,SAAS,EAAE9E,QAAQ,CAAC,CAAC9B,SAAS,CAClFC,QAAQ,IAAI;MACXnB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEkB,QAAQ,CAAC;IACzE,CAAC,EACAzD,KAAK,IAAI;MACRsC,OAAO,CAACtC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE;MACA,IAAIqF,eAAe,IAAIA,eAAe,CAACvH,SAAS,EAAE;QAChDuH,eAAe,CAACvH,SAAS,GAAGuH,eAAe,CAACvH,SAAS,CAAC+F,MAAM,CAAEyG,CAAS,IAAKA,CAAC,KAAKhF,QAAQ,CAAC;;IAE/F,CAAC,CACF;EACH;EAEAtL,cAAcA,CAAA;IACZ,IAAI,CAACI,SAAS,GAAG,EAAE;EACrB;EAEAiK,WAAWA,CAACjL,KAAa,EAAEI,OAAe;IACxC,IAAI,CAACY,SAAS,CAACuK,IAAI,CAAC;MAClBrL,SAAS,EAAE,IAAIuL,IAAI,EAAE;MACrBzL,KAAK,EAAEA,KAAK;MACZI,OAAO,EAAEA;KACV,CAAC;IAEF;IACA,IAAI,IAAI,CAACY,SAAS,CAACuG,MAAM,GAAG,EAAE,EAAE;MAC9B,IAAI,CAACvG,SAAS,GAAG,IAAI,CAACA,SAAS,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC;;EAE9C;EAEAkQ,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACrJ,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,CAACuJ,kBAAkB,CAAC,IAAI,CAACrJ,WAAW,CAAC,CAACsC,SAAS,CAChE,MAAK;MACH,IAAI,CAACa,WAAW,CAAC,MAAM,EAAE,mCAAmC,CAAC;MAC7D/B,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACrD,CAAC,EACAvC,KAAK,IAAI;MACR,IAAI,CAACqE,WAAW,CAAC,OAAO,EAAE,mCAAmCrE,KAAK,CAACxG,OAAO,EAAE,CAAC;MAC7E8I,OAAO,CAACtC,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;IACzE,CAAC,CACF;EACH;EAEAwK,iBAAiBA,CAAA;IACf,IAAI,CAACtI,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,CAACmC,WAAW,CAAC,MAAM,EAAE,4BAA4B,IAAI,CAACnC,eAAe,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;EACvG;EAEA/D,mBAAmBA,CAAA;IACjB,IAAI,CAAC6D,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAChD,IAAI,CAACqC,WAAW,CAAC,MAAM,EAAE,2BAA2B,IAAI,CAACrC,iBAAiB,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;EACxG;;;uBA3lBWpB,aAAa,EAAAtI,EAAA,CAAAmS,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArS,EAAA,CAAAmS,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAvS,EAAA,CAAAmS,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAzS,EAAA,CAAAmS,iBAAA,CAAAO,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAbrK,aAAa;MAAAsK,SAAA;MAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;UCtB1B/S,EAAA,CAAAC,cAAA,aAAqE;UAGtCD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,cAAgG;UAC9FD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAETH,EAAA,CAAAC,cAAA,aAA4B;UAECD,EAAA,CAAAmC,SAAA,YAA2B;UAACnC,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvEH,EAAA,CAAAC,cAAA,iBAA6F;UAApED,EAAA,CAAAmB,UAAA,2BAAA8R,wDAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA5J,aAAA,GAAA8J,MAAA;UAAA,EAA2B,oBAAAC,iDAAA;YAAA,OAAWH,GAAA,CAAA7E,aAAA,CAAA6E,GAAA,CAAA5J,aAAA,CAA4B;UAAA,EAAvC;UAClDpJ,EAAA,CAAAC,cAAA,oBAAyB;UACvBD,EAAA,CAAA2B,UAAA,KAAAyR,gCAAA,qBAAwG;UAC1GpT,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAAC,cAAA,oBAA2B;UACzBD,EAAA,CAAA2B,UAAA,KAAA0R,gCAAA,qBAA0G;UAC5GrT,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAA2B,UAAA,KAAA2R,gCAAA,qBAA2F;UAC7FtT,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAC,cAAA,eAAwC;UACVD,EAAA,CAAAmC,SAAA,aAA6B;UAACnC,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5EH,EAAA,CAAAC,cAAA,kBAAwH;UAA5FD,EAAA,CAAAmB,UAAA,2BAAAoS,wDAAAL,MAAA;YAAA,OAAAF,GAAA,CAAA1J,qBAAA,GAAA4J,MAAA;UAAA,EAAmC,oBAAAM,iDAAA;YAAA,OAAWR,GAAA,CAAAhC,qBAAA,CAAAgC,GAAA,CAAA1J,qBAAA,CAA4C;UAAA,EAAvD;UAC7DtJ,EAAA,CAAA2B,UAAA,KAAA8R,gCAAA,qBAAmF;UACrFzT,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAC,cAAA,eAA8B;UAEkBD,EAAA,CAAAmB,UAAA,2BAAAuS,uDAAAR,MAAA;YAAA,OAAAF,GAAA,CAAArJ,gBAAA,GAAAuJ,MAAA;UAAA,EAA8B;UAA1ElT,EAAA,CAAAG,YAAA,EAA2E;UAC3EH,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGZH,EAAA,CAAAC,cAAA,eAA0B;UAC+CD,EAAA,CAAAmB,UAAA,mBAAAwS,gDAAA;YAAA,OAASX,GAAA,CAAA7B,oBAAA,EAAsB;UAAA,EAAC;UACrGnR,EAAA,CAAAmC,SAAA,aAA2B;UAC3BnC,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA8C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE7DH,EAAA,CAAAC,cAAA,kBAA4F;UAAjED,EAAA,CAAAmB,UAAA,mBAAAyS,gDAAA;YAAA,OAASZ,GAAA,CAAAf,kBAAA,EAAoB;UAAA,EAAC;UACvDjS,EAAA,CAAAmC,SAAA,aAA2B;UAC7BnC,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA4I;UAA7GD,EAAA,CAAAmB,UAAA,mBAAA0S,gDAAA;YAAA,OAASb,GAAA,CAAAd,iBAAA,EAAmB;UAAA,EAAC;UAC1DlS,EAAA,CAAAmC,SAAA,aAAkC;UACpCnC,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAgF;UAAjDD,EAAA,CAAAmB,UAAA,mBAAA2S,gDAAA;YAAA,OAASd,GAAA,CAAA1E,SAAA,EAAW;UAAA,EAAC;UAClDtO,EAAA,CAAAmC,SAAA,aAA2B;UAC7BnC,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAkI;UAAlGD,EAAA,CAAAmB,UAAA,mBAAA4S,gDAAA;YAAA,OAASf,GAAA,CAAAxE,gBAAA,EAAkB;UAAA,EAAC;UAC1DxO,EAAA,CAAAmC,SAAA,aAAoG;UACtGnC,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAA2B,UAAA,KAAAqS,6BAAA,kBAYM;UACRhU,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,mBAAmD;UACjDD,EAAA,CAAA2B,UAAA,KAAAsS,6BAAA,kBAEM;UAENjU,EAAA,CAAA2B,UAAA,KAAAuS,6BAAA,oBAsFM;;UAGNlU,EAAA,CAAA2B,UAAA,KAAAwS,6BAAA,kBAIM;UAENnU,EAAA,CAAA2B,UAAA,KAAAyS,6BAAA,kBAGM;UACRpU,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAA2B,UAAA,KAAA0S,6BAAA,kBAMM;UAGNrU,EAAA,CAAA2B,UAAA,KAAA2S,6BAAA,kBAGM;UAGNtU,EAAA,CAAA2B,UAAA,KAAA4S,6BAAA,oBA8EM;UAKNvU,EAAA,CAAAC,cAAA,gBAAgF;UAAhDD,EAAA,CAAAmB,UAAA,sBAAAqT,iDAAA;YAAA,OAAYxB,GAAA,CAAA7F,WAAA,EAAa;UAAA,EAAC;UACxDnN,EAAA,CAAAC,cAAA,oBAMC;UADCD,EAAA,CAAAmB,UAAA,2BAAAsT,0DAAAvB,MAAA;YAAA,OAAiBF,GAAA,CAAAtE,OAAA,CAAAwE,MAAA,CAAqB;UAAA,EAAC;UACxClT,EAAA,CAAAG,YAAA,EAAW;UACZH,EAAA,CAAAC,cAAA,kBAAuG;UAC/FD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEnBH,EAAA,CAAAC,cAAA,kBAAiK;UAAnHD,EAAA,CAAAmB,UAAA,mBAAAuT,gDAAA;YAAA,OAAS1B,GAAA,CAAAlE,UAAA,EAAY;UAAA,EAAC;UAA6F9O,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrLH,EAAA,CAAAC,cAAA,kBAA8L;UAAhJD,EAAA,CAAAmB,UAAA,mBAAAwT,gDAAA;YAAA,OAAS3B,GAAA,CAAA7D,iBAAA,EAAmB;UAAA,EAAC;UAAmHnP,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1NH,EAAA,CAAAC,cAAA,kBAAuK;UAA3HD,EAAA,CAAAmB,UAAA,mBAAAyT,gDAAA;YAAA,OAAS5B,GAAA,CAAArC,QAAA,EAAU;UAAA,EAAC;UAAuG3Q,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzLH,EAAA,CAAA2B,UAAA,KAAAkT,6BAAA,kBAA6E;UAC/E7U,EAAA,CAAAG,YAAA,EAAO;;;UAlSmBH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8U,eAAA,KAAAC,GAAA,EAAA/B,GAAA,CAAAzJ,cAAA,EAAwC;UAKnCvJ,EAAA,CAAAO,SAAA,GAAoE;UAApEP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8U,eAAA,KAAAE,GAAA,EAAAhC,GAAA,CAAA9J,OAAA,IAAA8J,GAAA,CAAA/B,kBAAA,IAAA+B,GAAA,CAAAvJ,WAAA,EAAoE;UAC7FzJ,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAoC,kBAAA,MAAA4Q,GAAA,CAAA9J,OAAA,IAAA8J,GAAA,CAAA/B,kBAAA,eAAA+B,GAAA,CAAAvJ,WAAA,8BACF;UAK2BzJ,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAA5J,aAAA,CAA2B;UAEtBpJ,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAA3H,mBAAA,WAAgC;UAGhCrL,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAA3H,mBAAA,aAAkC;UAEpCrL,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAAtH,cAAA,GAAmB;UAKnB1L,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAA1J,qBAAA,CAAmC;UACrCtJ,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAA3J,cAAA,CAAiB;UAKGrJ,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAArJ,gBAAA,CAA8B;UAM9C3J,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8U,eAAA,KAAAE,GAAA,EAAAhC,GAAA,CAAAxJ,cAAA,EAAsC;UAE9DxJ,EAAA,CAAAO,SAAA,GAA8C;UAA9CP,EAAA,CAAAQ,iBAAA,CAAAwS,GAAA,CAAAxJ,cAAA,6BAA8C;UAKOxJ,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8U,eAAA,KAAAE,GAAA,EAAAhC,GAAA,CAAApJ,eAAA,EAAuC;UAMvC5J,EAAA,CAAAO,SAAA,GAAoE;UAApEP,EAAA,CAAAiV,WAAA,eAAAjC,GAAA,CAAAzJ,cAAA,mCAAoE;UACjHvJ,EAAA,CAAAO,SAAA,GAAiF;UAAjFP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAkV,eAAA,KAAAC,GAAA,EAAAnC,GAAA,CAAAzJ,cAAA,GAAAyJ,GAAA,CAAAzJ,cAAA,EAAiF;UAK1EvJ,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAI,UAAA,SAAA4S,GAAA,CAAAlR,SAAA,CAAAuG,MAAA,KAA0B;UAgB7CrI,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAI,UAAA,SAAA4S,GAAA,CAAA/J,QAAA,CAAAZ,MAAA,OAA2B;UAIRrI,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAoV,WAAA,SAAApC,GAAA,CAAA/J,QAAA,EAAqB;UAyFxCjJ,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,SAAA4S,GAAA,CAAAvJ,WAAA,CAAiB;UAMjBzJ,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAI,UAAA,UAAA4S,GAAA,CAAA9J,OAAA,IAAA8J,GAAA,CAAAnK,eAAA,MAAAmK,GAAA,CAAAvJ,WAAA,CAAkD;UAOpDzJ,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAI,UAAA,SAAA4S,GAAA,CAAAtJ,iBAAA,IAAAsJ,GAAA,CAAAjN,oBAAA,CAA+C;UAS/C/F,EAAA,CAAAO,SAAA,GAAwB;UAAxBP,EAAA,CAAAI,UAAA,SAAA4S,GAAA,CAAA/B,kBAAA,CAAwB;UAMxBjR,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAI,UAAA,SAAA4S,GAAA,CAAA5K,QAAA,IAAA4K,GAAA,CAAA5K,QAAA,CAAAC,MAAA,KAAqC;UAmFrCrI,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAI,UAAA,cAAA4S,GAAA,CAAAnI,WAAA,CAAyB;UAI3B7K,EAAA,CAAAO,SAAA,GAAyD;UAAzDP,EAAA,CAAAI,UAAA,aAAA4S,GAAA,CAAA9J,OAAA,IAAA8J,GAAA,CAAAnK,eAAA,IAAAmK,GAAA,CAAAlK,cAAA,CAAyD;UAIrC9I,EAAA,CAAAO,SAAA,GAAgF;UAAhFP,EAAA,CAAAI,UAAA,aAAA4S,GAAA,CAAAnI,WAAA,CAAAuC,OAAA,IAAA4F,GAAA,CAAA9J,OAAA,IAAA8J,GAAA,CAAAnK,eAAA,IAAAmK,GAAA,CAAAlK,cAAA,CAAgF;UAGjC9I,EAAA,CAAAO,SAAA,GAA+D;UAA/DP,EAAA,CAAAI,UAAA,aAAA4S,GAAA,CAAA/J,QAAA,CAAAZ,MAAA,UAAA2K,GAAA,CAAA9J,OAAA,IAAA8J,GAAA,CAAAlK,cAAA,CAA+D;UACxD9I,EAAA,CAAAO,SAAA,GAA+D;UAA/DP,EAAA,CAAAI,UAAA,aAAA4S,GAAA,CAAA/J,QAAA,CAAAZ,MAAA,UAAA2K,GAAA,CAAA9J,OAAA,IAAA8J,GAAA,CAAAlK,cAAA,CAA+D;UAC1E9I,EAAA,CAAAO,SAAA,GAA+D;UAA/DP,EAAA,CAAAI,UAAA,aAAA4S,GAAA,CAAA/J,QAAA,CAAAZ,MAAA,UAAA2K,GAAA,CAAA9J,OAAA,IAAA8J,GAAA,CAAAlK,cAAA,CAA+D;UAC1H9I,EAAA,CAAAO,SAAA,GAAoB;UAApBP,EAAA,CAAAI,UAAA,SAAA4S,GAAA,CAAAlK,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}