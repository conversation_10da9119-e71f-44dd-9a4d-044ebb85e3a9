<svg width="50" height="50" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
  <style>
    .spinner {
      transform-origin: center;
      animation: spin 1.5s linear infinite;
    }
    .circle {
      fill: none;
      stroke-width: 4;
      stroke-linecap: round;
    }
    .circle-bg {
      stroke: #e0e0e0;
    }
    .circle-progress {
      stroke: #2196f3;
      stroke-dasharray: 125;
      stroke-dashoffset: 125;
      animation: dash 1.5s ease-in-out infinite;
    }
    @keyframes spin {
      100% { transform: rotate(360deg); }
    }
    @keyframes dash {
      0% { stroke-dashoffset: 125; }
      50% { stroke-dashoffset: 0; }
      100% { stroke-dashoffset: -125; }
    }
  </style>
  <g class="spinner">
    <circle class="circle circle-bg" cx="25" cy="25" r="20"/>
    <circle class="circle circle-progress" cx="25" cy="25" r="20"/>
  </g>
  <!-- AI node in center -->
  <circle cx="25" cy="25" r="5" fill="#2196f3" opacity="0.8"/>
  <path d="M25,20 L25,16" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
  <path d="M25,34 L25,30" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
  <path d="M20,25 L16,25" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
  <path d="M34,25 L30,25" stroke="#2196f3" stroke-width="2" stroke-linecap="round"/>
</svg>
