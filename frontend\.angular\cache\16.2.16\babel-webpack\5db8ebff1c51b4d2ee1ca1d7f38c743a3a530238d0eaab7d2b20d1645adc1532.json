{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport let ProjectService = /*#__PURE__*/(() => {\n  class ProjectService {\n    constructor(apiService) {\n      this.apiService = apiService;\n      this.baseDir = 'C:/SourceProjects/AutonomousAI/projects';\n      console.log('[ProjectService] Initializing ProjectService...');\n      const platform = navigator.platform.toLowerCase();\n      if (platform.includes('linux')) {\n        this.baseDir = '/home/<USER>/SourceProjects/AutonomousAI/projects';\n        console.log('[ProjectService] Platform: Linux -> baseDir set to', this.baseDir);\n      } else if (platform.includes('mac')) {\n        this.baseDir = '/Users/<USER>/SourceProjects/AutonomousAI/projects';\n        console.log('[ProjectService] Platform: macOS -> baseDir set to', this.baseDir);\n      } else {\n        console.log('[ProjectService] Platform: Windows/Other -> baseDir remains', this.baseDir);\n      }\n    }\n    getProjects() {\n      console.log('[ProjectService] getProjects called');\n      return this.apiService.getProjects();\n    }\n    getProject(name) {\n      console.log('[ProjectService] getProject called for:', name);\n      return this.apiService.getProject(name);\n    }\n    createProject(name, description) {\n      console.log('[ProjectService] createProject called with:', {\n        name,\n        description\n      });\n      return this.apiService.createProject(name, description);\n    }\n    deleteProject(name) {\n      console.log('[ProjectService] deleteProject called for:', name);\n      return this.apiService.deleteProject(name);\n    }\n    getProjectFiles(projectName) {\n      console.log('[ProjectService] getProjectFiles called for:', projectName);\n      return this.apiService.getProjectFiles(projectName);\n    }\n    getProjectMessages(projectName) {\n      console.log('[ProjectService] getProjectMessages called for:', projectName);\n      return this.apiService.getProjectMessages(projectName);\n    }\n    deleteProjectMessages(projectName) {\n      console.log('[ProjectService] deleteProjectMessages called for:', projectName);\n      return this.apiService.deleteProjectMessages(projectName);\n    }\n    /**\n     * Save messages for a project to persist chat history and expanded state\n     * @param projectName Project name\n     * @param messages Array of messages to save\n     * @param chatExpanded Boolean indicating if chat is expanded\n     * @returns Observable with save result\n     */\n    saveProjectMessages(projectName, messages, chatExpanded = false) {\n      console.log('[ProjectService] saveProjectMessages called for:', projectName);\n      return this.apiService.saveProjectMessages(projectName, messages, chatExpanded);\n    }\n    getProjectDir(projectName) {\n      const path = `${this.baseDir}/${projectName}`;\n      console.log('[ProjectService] getProjectDir ->', path);\n      return path;\n    }\n    getBaseDir() {\n      console.log('[ProjectService] getBaseDir ->', this.baseDir);\n      return this.baseDir;\n    }\n    setBaseDir(dir) {\n      console.log('[ProjectService] setBaseDir called. New baseDir:', dir);\n      this.baseDir = dir;\n    }\n    resetProject(name) {\n      console.log('[ProjectService] resetProject called for:', name);\n      return this.apiService.resetProject(name);\n    }\n    exportProjectChat(name) {\n      console.log('[ProjectService] exportProjectChat called for:', name);\n      return this.apiService.exportProjectChat(name);\n    }\n    deleteAllProjects() {\n      console.log('[ProjectService] deleteAllProjects called');\n      return this.apiService.deleteAllProjects();\n    }\n    resetContextMemory(projectName) {\n      console.log('[ProjectService] resetContextMemory called for:', projectName);\n      return this.apiService.resetContextMemory(projectName);\n    }\n    static {\n      this.ɵfac = function ProjectService_Factory(t) {\n        return new (t || ProjectService)(i0.ɵɵinject(i1.ApiService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProjectService,\n        factory: ProjectService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ProjectService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}