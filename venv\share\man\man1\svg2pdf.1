.TH SVG2PDF "1" "March 2021" "python-svglib" "User Commands"
.SH NAME
svg2pdf \- A converter from SVG to PDF (via ReportLab Graphics)
.SH SYNOPSIS
.B svg2pdf
[\fI\,OPTIONS\/\fR]... [\fI\,-o PATH_PAT\/\fR] [\fI\,PATH\/\fR]...
.SH DESCRIPTION
.PP
.TP
\fB\-h, \-\-help\fR
show a help message and exit
.TP
\fB\-v, \-\-version\fR
print version number and exit
.TP
\fB\-o, \-\-output PATH_PAT\fR
Set output path (incl. the placeholders: dirname, basename,base, ext, now) in
both, %(name)s and {name} notations.
.TP

.SH "REPORTING BUGS"
Report bugs to  <https://github.com/deeplook/svglib/issues>
