{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/socket.service\";\nimport * as i3 from \"../../services/project.service\";\nimport * as i4 from \"../../services/agent.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"./reverse.pipe\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatComponent_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r14.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(model_r14.name);\n  }\n}\nfunction ChatComponent_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(model_r15.name);\n  }\n}\nfunction ChatComponent_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r16.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(model_r16.name);\n  }\n}\nfunction ChatComponent_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const llm_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", llm_r17.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(llm_r17.name);\n  }\n}\nfunction ChatComponent_div_43_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"span\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 56);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 57);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const log_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"log-\" + log_r19.level);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 4, log_r19.timestamp, \"HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(log_r19.level.toUpperCase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r19.message);\n  }\n}\nfunction ChatComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"span\");\n    i0.ɵɵtext(3, \"\\uD83D\\uDD0D Debug Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_43_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.clearDebugLogs());\n    });\n    i0.ɵɵtext(5, \"Clear\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 52);\n    i0.ɵɵtemplate(7, ChatComponent_div_43_div_7_Template, 8, 7, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.debugLogs.slice(-10));\n  }\n}\nfunction ChatComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"p\");\n    i0.ɵɵtext(2, \"No messages yet. Start a conversation with the AI agent.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_div_47_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"U\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"AI\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"S\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83C\\uDF10\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCBB\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u26A0\\uFE0F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE4\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE5\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDC64 You\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDD16 AI Agent\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u2699\\uFE0F System\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDD0D Browser\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 OpenAI\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 LM Studio\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCBB Local LLM\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCBB Terminal\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 OpenAI\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 LM Studio\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u26A0\\uFE0F Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCCB Plan\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE3 Notification\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE4 API Request\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE5 API Response\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(message_r22.metadata.modelId);\n  }\n}\nfunction ChatComponent_div_47_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵelement(1, \"div\", 75)(2, \"div\", 75)(3, \"div\", 75);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Execution time: \", message_r22.metadata.executionTime, \"ms\");\n  }\n}\nfunction ChatComponent_div_47_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_47_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9) {\n  return {\n    \"user-message\": a0,\n    \"agent-message\": a1,\n    \"system-message\": a2,\n    \"browser-message\": a3,\n    \"openai-message\": a4,\n    \"llm-message\": a5,\n    \"error-message\": a6,\n    \"api-request-message\": a7,\n    \"api-response-message\": a8,\n    \"streaming\": a9\n  };\n};\nconst _c2 = function (a0, a1, a2, a3, a4, a5, a6, a7, a8) {\n  return {\n    \"user-avatar\": a0,\n    \"ai-avatar\": a1,\n    \"system-avatar\": a2,\n    \"browser-avatar\": a3,\n    \"openai-avatar\": a4,\n    \"llm-avatar\": a5,\n    \"error-avatar\": a6,\n    \"api-request-avatar\": a7,\n    \"api-response-avatar\": a8\n  };\n};\nfunction ChatComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵtemplate(2, ChatComponent_div_47_span_2_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(3, ChatComponent_div_47_span_3_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(4, ChatComponent_div_47_span_4_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(5, ChatComponent_div_47_span_5_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(6, ChatComponent_div_47_span_6_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(7, ChatComponent_div_47_span_7_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(8, ChatComponent_div_47_span_8_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(9, ChatComponent_div_47_span_9_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(10, ChatComponent_div_47_span_10_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(11, ChatComponent_div_47_span_11_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 62)(13, \"div\", 63)(14, \"span\", 64);\n    i0.ɵɵtemplate(15, ChatComponent_div_47_span_15_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(16, ChatComponent_div_47_span_16_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(17, ChatComponent_div_47_span_17_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(18, ChatComponent_div_47_span_18_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(19, ChatComponent_div_47_span_19_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(20, ChatComponent_div_47_span_20_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(21, ChatComponent_div_47_span_21_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(22, ChatComponent_div_47_span_22_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(23, ChatComponent_div_47_span_23_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(24, ChatComponent_div_47_span_24_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(25, ChatComponent_div_47_span_25_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(26, ChatComponent_div_47_span_26_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(27, ChatComponent_div_47_span_27_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(28, ChatComponent_div_47_span_28_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(29, ChatComponent_div_47_span_29_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 65);\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, ChatComponent_div_47_span_33_Template, 2, 1, \"span\", 66);\n    i0.ɵɵtemplate(34, ChatComponent_div_47_span_34_Template, 4, 0, \"span\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(35, \"div\", 68);\n    i0.ɵɵtemplate(36, ChatComponent_div_47_div_36_Template, 3, 1, \"div\", 69);\n    i0.ɵɵelementStart(37, \"div\", 70)(38, \"div\", 71)(39, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_47_Template_button_click_39_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r57);\n      const message_r22 = restoredCtx.$implicit;\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.addReaction(message_r22.id, \"like\"));\n    });\n    i0.ɵɵtext(40, \" \\uD83D\\uDC4D \");\n    i0.ɵɵtemplate(41, ChatComponent_div_47_span_41_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_47_Template_button_click_42_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r57);\n      const message_r22 = restoredCtx.$implicit;\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.addReaction(message_r22.id, \"dislike\"));\n    });\n    i0.ɵɵtext(43, \" \\uD83D\\uDC4E \");\n    i0.ɵɵtemplate(44, ChatComponent_div_47_span_44_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_47_Template_button_click_45_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r57);\n      const message_r22 = restoredCtx.$implicit;\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.addReaction(message_r22.id, \"love\"));\n    });\n    i0.ɵɵtext(46, \" \\u2764\\uFE0F \");\n    i0.ɵɵtemplate(47, ChatComponent_div_47_span_47_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const message_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunctionV(44, _c1, [message_r22.sender === \"user\", message_r22.sender === \"agent\", message_r22.sender === \"system\", message_r22.messageType === \"browser\", message_r22.messageType === \"openai\", message_r22.messageType === \"local_llm\", message_r22.messageType === \"error\", message_r22.messageType === \"api_request\", message_r22.messageType === \"api_response\", message_r22.sender === \"agent\" && !message_r22.isComplete]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunctionV(55, _c2, [message_r22.sender === \"user\", message_r22.sender === \"agent\", message_r22.sender === \"system\", message_r22.messageType === \"browser\", message_r22.messageType === \"openai\", message_r22.messageType === \"local_llm\", message_r22.messageType === \"error\", message_r22.messageType === \"api_request\", message_r22.messageType === \"api_response\"]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"agent\" && !message_r22.messageType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"system\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"browser\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"local_llm\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"error\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"system_notification\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"api_request\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"api_response\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"agent\" && !message_r22.messageType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"system\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"browser\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"lm_studio\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"local_llm\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"terminal\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"llm_openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"llm_lm_studio\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"error\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"plan\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"system_notification\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"api_request\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.messageType === \"api_response\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(32, 41, message_r22.timestamp, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r22.metadata == null ? null : message_r22.metadata.modelId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.sender === \"agent\" && !message_r22.isComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", message_r22.content, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r22.metadata && message_r22.metadata.executionTime);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", message_r22.reactions && message_r22.reactions.includes(\"like\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r22.reactions && message_r22.reactions.includes(\"like\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", message_r22.reactions && message_r22.reactions.includes(\"dislike\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r22.reactions && message_r22.reactions.includes(\"dislike\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", message_r22.reactions && message_r22.reactions.includes(\"love\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r22.reactions && message_r22.reactions.includes(\"love\"));\n  }\n}\nfunction ChatComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵelement(1, \"div\", 75)(2, \"div\", 75)(3, \"div\", 75);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵelement(1, \"div\", 80);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"AI Agent is thinking...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82)(2, \"h4\");\n    i0.ɵɵtext(3, \"Agent Thinking Process\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_51_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.toggleAgentThinking());\n    });\n    i0.ɵɵtext(5, \"Close\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\", 84);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r10.agentThinkingContent);\n  }\n}\nfunction ChatComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"span\", 80);\n    i0.ɵɵelementStart(2, \"span\", 86);\n    i0.ɵɵtext(3, \"The AI assistant is working on a complex, long-running task. This may take several minutes. Please do not close this window.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵtext(1, \"GPT\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115);\n    i0.ɵɵtext(1, \"Local LLM\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵtext(1, \"HOT\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtext(1, \"Web Search Results\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118);\n    i0.ɵɵtext(1, \"File Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"span\");\n    i0.ɵɵtext(2, \"Was this helpful?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 120)(4, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_53_div_31_div_21_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const i_r64 = i0.ɵɵnextContext().index;\n      const ctx_r72 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r72.setSubtaskFeedback(i_r64, \"up\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_53_div_31_div_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const i_r64 = i0.ɵɵnextContext().index;\n      const ctx_r75 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r75.setSubtaskFeedback(i_r64, \"down\"));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const subtask_r63 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"selected\", subtask_r63.feedback === \"up\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"selected\", subtask_r63.feedback === \"down\");\n  }\n}\nfunction ChatComponent_div_53_div_31_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r80 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_53_div_31_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r80);\n      const i_r64 = i0.ɵɵnextContext().index;\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.retrySubtask(i_r64));\n    });\n    i0.ɵɵtext(1, \" Retry \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_53_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98)(1, \"div\", 99)(2, \"div\", 100);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 101);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ChatComponent_div_53_div_31_div_6_Template, 2, 0, \"div\", 102);\n    i0.ɵɵtemplate(7, ChatComponent_div_53_div_31_div_7_Template, 2, 0, \"div\", 103);\n    i0.ɵɵtemplate(8, ChatComponent_div_53_div_31_div_8_Template, 2, 0, \"div\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 105)(10, \"div\", 106);\n    i0.ɵɵtext(11, \"\\u25B6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 107)(15, \"div\", 108);\n    i0.ɵɵtext(16, \"Output:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 109);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ChatComponent_div_53_div_31_div_19_Template, 2, 0, \"div\", 110);\n    i0.ɵɵtemplate(20, ChatComponent_div_53_div_31_div_20_Template, 2, 0, \"div\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, ChatComponent_div_53_div_31_div_21_Template, 6, 4, \"div\", 112);\n    i0.ɵɵtemplate(22, ChatComponent_div_53_div_31_button_22_Template, 2, 0, \"button\", 113);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subtask_r63 = ctx.$implicit;\n    const i_r64 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r64 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(subtask_r63.subtask.type || \"COMMAND\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.model_type === \"openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.model_type === \"local\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.web_research_used);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(subtask_r63.subtask.description || \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"error\", subtask_r63.error);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", subtask_r63.result || \"Processing...\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.web_results);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.file_diff);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r63.error);\n  }\n}\nfunction ChatComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"h4\");\n    i0.ɵɵtext(3, \"Autonomous Agent Workflow\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 89)(5, \"div\", 90)(6, \"span\");\n    i0.ɵɵtext(7, \"\\uD83D\\uDD0D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\");\n    i0.ɵɵtext(9, \"Planning\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"div\", 91);\n    i0.ɵɵelementStart(11, \"div\", 92)(12, \"span\");\n    i0.ɵɵtext(13, \"\\uD83C\\uDFA8\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\");\n    i0.ɵɵtext(15, \"Design\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(16, \"div\", 91);\n    i0.ɵɵelementStart(17, \"div\", 93)(18, \"span\");\n    i0.ɵɵtext(19, \"\\uD83D\\uDEE0\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\");\n    i0.ɵɵtext(21, \"Implementation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(22, \"div\", 91);\n    i0.ɵɵelementStart(23, \"div\", 94)(24, \"span\");\n    i0.ɵɵtext(25, \"\\uD83E\\uDDEA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\");\n    i0.ɵɵtext(27, \"Testing\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 95);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 96);\n    i0.ɵɵtemplate(31, ChatComponent_div_53_div_31_Template, 23, 13, \"div\", 97);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r12.currentStage >= 4);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r12.completedSubtasks, \"/\", ctx_r12.subtasks.length, \" steps completed \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.subtasks);\n  }\n}\nfunction ChatComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵtext(1, \"Saving messages...\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    \"expanded\": a0\n  };\n};\nconst _c4 = function (a0) {\n  return {\n    \"active\": a0\n  };\n};\nconst _c5 = function (a0, a1) {\n  return {\n    \"fa-chevron-up\": a0,\n    \"fa-chevron-down\": a1\n  };\n};\nexport let ChatComponent = /*#__PURE__*/(() => {\n  class ChatComponent {\n    constructor(fb, socketService, projectService, agentService) {\n      this.fb = fb;\n      this.socketService = socketService;\n      this.projectService = projectService;\n      this.agentService = agentService;\n      this.projectName = '';\n      this.messagesLoading = false;\n      this.messagesSaving = false;\n      this.messageEvent = new EventEmitter();\n      this.chatExpandChange = new EventEmitter();\n      this.messages = [];\n      this.loading = false;\n      this.models = [];\n      this.selectedModel = 'deepseek/deepseek-coder';\n      this.localLlmModels = [{\n        id: 'mistral-nemo-instruct-2407',\n        name: 'Mistral Nemo Instruct 2407'\n      }\n      // Add more local LLM models here if needed\n      ];\n\n      this.selectedLocalLlmModel = 'mistral-nemo-instruct-2407';\n      this.isChatExpanded = false;\n      this.subtasks = [];\n      this.autonomousMode = false;\n      this.agentTyping = false;\n      this.agentThinkingContent = '';\n      this.showAgentThinking = false;\n      this.streamingEnabled = true;\n      this.showApiPayloads = true;\n      this.apiRequests = [];\n      this.apiResponses = [];\n      this.debugLogs = [];\n    }\n    ngOnInit() {\n      console.log('[ChatComponent] ngOnInit called');\n      this.initForm();\n      this.loadMessages();\n      this.loadModels();\n      this.setupSocketListeners();\n    }\n    ngAfterViewChecked() {\n      this.scrollToBottom();\n    }\n    scrollToBottom() {\n      try {\n        if (this.messagesContainer && this.messagesContainer.nativeElement) {\n          this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\n        }\n      } catch (err) {}\n    }\n    initForm() {\n      console.log('[ChatComponent] Initializing form');\n      this.messageForm = this.fb.group({\n        message: ['', Validators.required]\n      });\n    }\n    loadMessages() {\n      console.log('[ChatComponent] loadMessages called for project:', this.projectName);\n      if (!this.projectName) {\n        console.warn('[ChatComponent] No projectName found. Skipping loadMessages.');\n        return;\n      }\n      if (!this.messagesLoading) {\n        this.loading = true;\n      }\n      this.projectService.getProjectMessages(this.projectName).subscribe(response => {\n        console.log('[ChatComponent] Project messages loaded:', response);\n        this.messages = response.messages || [];\n        this.loading = false;\n      }, error => {\n        console.error('[ChatComponent] ❌ Error loading messages:', error);\n        this.loading = false;\n      });\n    }\n    loadModels() {\n      console.log('[ChatComponent] loadModels called');\n      this.agentService.getModels().subscribe(response => {\n        console.log('[ChatComponent] Models loaded:', response);\n        this.models = response.models || [];\n      }, error => {\n        console.error('[ChatComponent] ❌ Error loading models:', error);\n      });\n    }\n    /**\n     * Gets models filtered by provider\n     */\n    getModelsByProvider(provider) {\n      return this.models.filter(model => model.id.startsWith(`${provider}/`));\n    }\n    /**\n     * Gets models that don't belong to specified providers\n     */\n    getOtherModels() {\n      const knownProviders = ['openai', 'deepseek'];\n      return this.models.filter(model => !knownProviders.some(provider => model.id.startsWith(`${provider}/`)));\n    }\n    setupSocketListeners() {\n      console.log('[ChatComponent] Setting up socket listeners');\n      this.socketService.on('agent_message').subscribe(data => {\n        console.log('[ChatComponent] 🔁 Received socket \"agent_message\":', data);\n        this.addDebugLog('info', `Agent message received: ${data.message?.substring(0, 50)}...`);\n        if (data.project_name === this.projectName) {\n          this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\n          // Determine the message type from incoming data\n          const messageType = data.message_type || 'agent';\n          this.messages.push({\n            id: data.message_id || `msg-${Date.now()}`,\n            sender: 'agent',\n            content: data.message,\n            timestamp: new Date(),\n            isAgentWorkingPlaceholder: false,\n            messageType: messageType,\n            reactions: []\n          });\n          this.loading = false;\n          console.log('[ChatComponent] Message added to chat from agent');\n        }\n      });\n      this.socketService.on('agent_typing').subscribe(data => {\n        console.log('[ChatComponent] 🔁 Received socket \"agent_typing\":', data);\n        if (data.project_name === this.projectName) {\n          this.agentTyping = data.is_typing;\n          // If not typing anymore, remove any placeholder messages\n          if (!data.is_typing) {\n            this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\n          }\n        }\n      });\n      this.socketService.on('agent_stream_token').subscribe(data => {\n        console.log('[ChatComponent] 🔁 Received socket \"agent_stream_token\":', data);\n        if (data.project_name === this.projectName) {\n          // Find the last agent message or create a new one if none exists\n          let lastAgentMessage = this.messages.find(m => m.sender === 'agent' && !m.isComplete);\n          if (!lastAgentMessage) {\n            lastAgentMessage = {\n              id: `stream-${Date.now()}`,\n              sender: 'agent',\n              content: '',\n              timestamp: new Date(),\n              isComplete: false,\n              reactions: []\n            };\n            this.messages.push(lastAgentMessage);\n          }\n          // Append the token to the message content\n          lastAgentMessage.content += data.token;\n          this.scrollToBottom();\n        }\n      });\n      this.socketService.on('agent_complete').subscribe(data => {\n        console.log('[ChatComponent] ✅ Received socket \"agent_complete\":', data);\n        if (data.project_name === this.projectName) {\n          this.loading = false;\n          this.agentTyping = false;\n          // Mark all agent messages as complete\n          this.messages.forEach(message => {\n            if (message.sender === 'agent') {\n              message.isComplete = true;\n            }\n          });\n          // Remove any placeholder messages\n          this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\n          console.log('[ChatComponent] Loading state cleared after agent_complete');\n        }\n      });\n      this.socketService.on('agent_thinking').subscribe(data => {\n        console.log('[ChatComponent] 🧠 Received socket \"agent_thinking\":', data);\n        if (data.project_name === this.projectName && this.showAgentThinking) {\n          // Display the agent's thought process in a special UI element if debugging is enabled\n          this.agentThinkingContent = data.thinking;\n        }\n      });\n      this.socketService.on('message_reaction').subscribe(data => {\n        console.log('[ChatComponent] 👍 Received socket \"message_reaction\":', data);\n        if (data.project_name === this.projectName) {\n          // Find the message and add the reaction\n          const messageToUpdate = this.messages.find(m => m.id === data.message_id);\n          if (messageToUpdate) {\n            if (!messageToUpdate.reactions) {\n              messageToUpdate.reactions = [];\n            }\n            if (!messageToUpdate.reactions.includes(data.reaction)) {\n              messageToUpdate.reactions.push(data.reaction);\n            }\n          }\n        }\n      });\n      // Listen for any error events\n      this.socketService.on('error').subscribe(data => {\n        console.error('[ChatComponent] ❌ Socket error:', data);\n        this.addDebugLog('error', `Socket error: ${JSON.stringify(data)}`);\n      });\n      // Listen for agent errors specifically\n      this.socketService.on('agent_error').subscribe(data => {\n        console.error('[ChatComponent] ❌ Agent error:', data);\n        this.addDebugLog('error', `Agent error: ${data.error || 'Unknown error'}`);\n        if (data.project_name === this.projectName) {\n          this.loading = false;\n          this.agentTyping = false;\n          // Add error message to chat\n          this.messages.push({\n            id: `error-${Date.now()}`,\n            sender: 'system',\n            messageType: 'error',\n            content: `<strong>Agent Error:</strong><br>${data.error || 'Unknown error occurred'}`,\n            timestamp: new Date(),\n            reactions: []\n          });\n        }\n      });\n    }\n    sendMessage() {\n      if (this.messageForm.invalid) {\n        return;\n      }\n      const messageContent = this.messageForm.get('message')?.value;\n      if (!messageContent || !this.projectName) {\n        return;\n      }\n      // Add user message to the chat\n      const userMessageId = `msg-${Date.now()}`;\n      this.messages.push({\n        id: userMessageId,\n        sender: 'user',\n        content: messageContent,\n        timestamp: new Date(),\n        reactions: []\n      });\n      // Create request payload\n      const requestPayload = {\n        project_name: this.projectName,\n        message: messageContent,\n        model_id: this.selectedModel,\n        local_llm_model_id: this.selectedLocalLlmModel,\n        streaming_enabled: this.streamingEnabled\n      };\n      // Store request\n      const requestEntry = {\n        timestamp: new Date(),\n        type: 'request',\n        endpoint: `/projects/${this.projectName}/messages`,\n        payload: requestPayload\n      };\n      this.apiRequests.push(requestEntry);\n      // If API payloads are visible, add to messages\n      if (this.showApiPayloads) {\n        this.messages.push({\n          id: `api-req-${Date.now()}`,\n          sender: 'system',\n          messageType: 'api_request',\n          content: `<strong>API Request:</strong><br><pre>${JSON.stringify(requestPayload, null, 2)}</pre>`,\n          timestamp: new Date(),\n          reactions: []\n        });\n      }\n      // Reset the form\n      this.messageForm.reset();\n      // Show loading indicator\n      this.loading = true;\n      // Send to API\n      this.addDebugLog('info', `Sending message with model: ${this.selectedModel}`);\n      if (this.streamingEnabled) {\n        // For streaming, we handle via sockets\n        this.addDebugLog('info', 'Using streaming mode via WebSocket');\n        this.socketService.sendMessage(this.projectName, messageContent, this.selectedModel);\n      } else {\n        // For non-streaming, we make a direct API call\n        this.agentService.sendMessage(this.projectName, messageContent, this.selectedModel, this.selectedLocalLlmModel, false).subscribe(response => {\n          // Store response\n          const responseEntry = {\n            timestamp: new Date(),\n            type: 'response',\n            endpoint: `/projects/${this.projectName}/messages`,\n            payload: response\n          };\n          this.apiResponses.push(responseEntry);\n          // If API payloads are visible, add to messages\n          if (this.showApiPayloads) {\n            this.messages.push({\n              id: `api-res-${Date.now()}`,\n              sender: 'system',\n              messageType: 'api_response',\n              content: `<strong>API Response:</strong><br><pre>${JSON.stringify(response, null, 2)}</pre>`,\n              timestamp: new Date(),\n              reactions: []\n            });\n          }\n          this.loading = false;\n        }, error => {\n          console.error('[ChatComponent] ❌ Error sending message:', error);\n          // Store error response\n          const errorEntry = {\n            timestamp: new Date(),\n            type: 'error',\n            endpoint: `/projects/${this.projectName}/messages`,\n            payload: error\n          };\n          this.apiResponses.push(errorEntry);\n          // Add error message\n          this.messages.push({\n            id: `error-${Date.now()}`,\n            sender: 'system',\n            messageType: 'error',\n            content: `<strong>API Error:</strong><br><pre>${JSON.stringify(error, null, 2)}</pre>`,\n            timestamp: new Date(),\n            reactions: []\n          });\n          this.loading = false;\n        });\n      }\n    }\n    onModelChange(modelId) {\n      console.log('[ChatComponent] Model changed to:', modelId);\n      this.selectedModel = modelId;\n    }\n    deleteMessage(index) {\n      this.messages.splice(index, 1);\n    }\n    clearChat() {\n      if (!this.projectName) return;\n      // Clear messages in UI\n      this.messages = [];\n      // Call backend API to delete chat history\n      this.projectService.deleteProjectMessages(this.projectName).subscribe(() => {\n        console.log('[ChatComponent] Chat history deleted on backend');\n      }, error => {\n        console.error('[ChatComponent] Error deleting chat history:', error);\n      });\n    }\n    toggleChatExpand() {\n      this.isChatExpanded = !this.isChatExpanded;\n      this.chatExpandChange.emit(this.isChatExpanded);\n    }\n    onEnter(event) {\n      if (event.shiftKey) {\n        return; // allow newline\n      }\n\n      event.preventDefault();\n      this.sendMessage();\n    }\n    exportChat() {\n      if (!this.projectName) return;\n      this.projectService.exportProjectChat(this.projectName).subscribe(response => {\n        alert('Chat exported: ' + (response?.file_path || 'Success'));\n      }, error => {\n        alert('Failed to export chat: ' + (error?.error?.detail || error));\n      });\n    }\n    exportDynamicChat() {\n      if (!this.messages.length) return;\n      // Format chat as text\n      const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\\n');\n      const blob = new Blob([chatText], {\n        type: 'text/plain'\n      });\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n      const filename = `${this.projectName || 'chat'}-dynamic-${timestamp}.txt`;\n      a.href = url;\n      a.download = filename;\n      document.body.appendChild(a);\n      a.click();\n      setTimeout(() => {\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      }, 0);\n    }\n    copyChat() {\n      const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\\n');\n      navigator.clipboard.writeText(chatText).then(() => alert('Chat copied to clipboard!'), () => alert('Failed to copy chat to clipboard.'));\n    }\n    onLocalLlmModelChange(modelId) {\n      this.selectedLocalLlmModel = modelId;\n      // You can add logic here to notify the backend or update the session if needed\n      console.log('[ChatComponent] Local LLM model changed to:', modelId);\n    }\n    get completedSubtasks() {\n      if (!this.subtasks) return 0;\n      return this.subtasks.filter(subtask => !subtask.error).length;\n    }\n    retrySubtask(index) {\n      // Simulate retry: clear error and result, set to loading, then re-run (in real app, call backend)\n      const subtask = this.subtasks[index];\n      subtask.error = null;\n      subtask.result = 'Retrying...';\n      // Simulate async retry (replace with real backend call)\n      setTimeout(() => {\n        subtask.result = 'Retried result (simulated)';\n        subtask.error = null;\n      }, 1500);\n    }\n    setSubtaskFeedback(index, feedback) {\n      this.subtasks[index].feedback = feedback;\n    }\n    get longTaskInProgress() {\n      if (this.loading) return true;\n      if (this.subtasks && this.subtasks.length > 0) {\n        return this.subtasks.some(s => !s.result && !s.error);\n      }\n      return false;\n    }\n    toggleAutonomousMode() {\n      this.autonomousMode = !this.autonomousMode;\n    }\n    /**\n     * Formats a unified diff string as HTML with basic syntax highlighting for added, removed, and context lines.\n     * @param diff The unified diff string\n     * @returns HTML string\n     */\n    formatFileDiff(diff) {\n      if (!diff) return '';\n      // Escape HTML\n      const escape = s => s.replace(/[&<>]/g, c => ({\n        '&': '&amp;',\n        '<': '&lt;',\n        '>': '&gt;'\n      })[c] || c);\n      return '<pre>' + diff.split('\\n').map(line => {\n        if (line.startsWith('+') && !line.startsWith('+++')) {\n          return `<span class='diff-added'>${escape(line)}</span>`;\n        } else if (line.startsWith('-') && !line.startsWith('---')) {\n          return `<span class='diff-removed'>${escape(line)}</span>`;\n        } else if (line.startsWith('@@')) {\n          return `<span class='diff-hunk'>${escape(line)}</span>`;\n        } else {\n          return escape(line);\n        }\n      }).join('\\n') + '</pre>';\n    }\n    /**\n     * Returns the current workflow stage for the progress indicator in the chat UI.\n     * 1 = Planning, 2 = Design, 3 = Implementation, 4 = Testing\n     */\n    get currentStage() {\n      if (!this.subtasks || this.subtasks.length === 0) return 0;\n      // If all subtasks are completed, return 4 (Testing)\n      if (this.subtasks.every(s => s.completed || s.result || s.error)) return 4;\n      // Otherwise, estimate stage based on subtask type or index\n      // (You can refine this logic as needed)\n      let stage = 1;\n      for (const subtask of this.subtasks) {\n        if (subtask.subtask?.type === 'design') stage = Math.max(stage, 2);else if (subtask.subtask?.type === 'implementation') stage = Math.max(stage, 3);else if (subtask.subtask?.type === 'testing') stage = Math.max(stage, 4);\n      }\n      return stage;\n    }\n    addReaction(messageId, reaction) {\n      console.log(`[ChatComponent] Adding reaction ${reaction} to message ${messageId}`);\n      // Optimistically update UI\n      const messageToUpdate = this.messages.find(m => m.id === messageId);\n      if (messageToUpdate) {\n        if (!messageToUpdate.reactions) {\n          messageToUpdate.reactions = [];\n        }\n        if (!messageToUpdate.reactions.includes(reaction)) {\n          messageToUpdate.reactions.push(reaction);\n        }\n      }\n      // Send to backend\n      this.agentService.addMessageReaction(this.projectName, messageId, reaction).subscribe(response => {\n        console.log('[ChatComponent] ✅ Reaction added successfully:', response);\n      }, error => {\n        console.error('[ChatComponent] ❌ Error adding reaction:', error);\n        // Remove the reaction if it failed\n        if (messageToUpdate && messageToUpdate.reactions) {\n          messageToUpdate.reactions = messageToUpdate.reactions.filter(r => r !== reaction);\n        }\n      });\n    }\n    clearDebugLogs() {\n      this.debugLogs = [];\n    }\n    addDebugLog(level, message) {\n      this.debugLogs.push({\n        timestamp: new Date(),\n        level: level,\n        message: message\n      });\n      // Keep only the last 50 logs to prevent memory issues\n      if (this.debugLogs.length > 50) {\n        this.debugLogs = this.debugLogs.slice(-50);\n      }\n    }\n    resetContextMemory() {\n      if (!this.projectName) return;\n      this.projectService.resetContextMemory(this.projectName).subscribe(() => {\n        this.addDebugLog('info', 'Context memory reset successfully');\n        console.log('[ChatComponent] Context memory reset');\n      }, error => {\n        this.addDebugLog('error', `Failed to reset context memory: ${error.message || error}`);\n        console.error('[ChatComponent] Error resetting context memory:', error);\n      });\n    }\n    toggleApiPayloads() {\n      this.showApiPayloads = !this.showApiPayloads;\n      this.addDebugLog('info', `API payloads visibility: ${this.showApiPayloads ? 'enabled' : 'disabled'}`);\n    }\n    toggleAgentThinking() {\n      this.showAgentThinking = !this.showAgentThinking;\n      this.addDebugLog('info', `Agent thinking display: ${this.showAgentThinking ? 'enabled' : 'disabled'}`);\n    }\n    static {\n      this.ɵfac = function ChatComponent_Factory(t) {\n        return new (t || ChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SocketService), i0.ɵɵdirectiveInject(i3.ProjectService), i0.ɵɵdirectiveInject(i4.AgentService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ChatComponent,\n        selectors: [[\"app-chat\"]],\n        viewQuery: function ChatComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          }\n        },\n        inputs: {\n          projectName: \"projectName\",\n          messagesLoading: \"messagesLoading\",\n          messagesSaving: \"messagesSaving\"\n        },\n        outputs: {\n          messageEvent: \"messageEvent\",\n          chatExpandChange: \"chatExpandChange\"\n        },\n        decls: 66,\n        vars: 43,\n        consts: [[1, \"chat-container\", 3, \"ngClass\"], [1, \"chat-header\"], [1, \"agent-title\"], [1, \"agent-icon\"], [1, \"agent-status\", 3, \"ngClass\"], [1, \"model-controls\"], [1, \"model-selector\", \"cloud-model\"], [\"for\", \"modelSelect\"], [1, \"fa\", \"fa-cloud\"], [\"id\", \"modelSelect\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"label\", \"OpenAI\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"DeepSeek\"], [1, \"model-selector\", \"local-model\"], [\"for\", \"localLlmSelect\"], [1, \"fa\", \"fa-desktop\"], [\"id\", \"localLlmSelect\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"streaming-toggle\"], [\"for\", \"streamingToggle\"], [\"type\", \"checkbox\", \"id\", \"streamingToggle\", 3, \"ngModel\", \"ngModelChange\"], [1, \"chat-actions\"], [\"title\", \"Toggle between autonomous and assisted mode\", 1, \"mode-toggle-btn\", 3, \"ngClass\", \"click\"], [1, \"fa\", \"fa-robot\"], [\"title\", \"Reset conversation memory\", 1, \"memory-btn\", 3, \"click\"], [1, \"fa\", \"fa-brain\"], [\"title\", \"Toggle API payloads visibility\", 1, \"api-toggle-btn\", 3, \"ngClass\", \"click\"], [1, \"fa\", \"fa-exchange-alt\"], [\"title\", \"Clear chat history\", 1, \"clear-chat-btn\", 3, \"click\"], [1, \"fa\", \"fa-trash\"], [1, \"expand-chat-btn\", 3, \"click\"], [1, \"fa\", 3, \"ngClass\"], [\"class\", \"debug-logs\", 4, \"ngIf\"], [1, \"messages-container\"], [\"messagesContainer\", \"\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"typing-indicator\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"agent-thinking-panel\", 4, \"ngIf\"], [\"class\", \"long-task-banner\", 4, \"ngIf\"], [\"class\", \"autonomous-workflow\", 4, \"ngIf\"], [1, \"message-form\", 3, \"formGroup\", \"ngSubmit\"], [\"formControlName\", \"message\", \"placeholder\", \"Type your message here...\", \"rows\", \"3\", 3, \"disabled\", \"keydown.enter\"], [\"type\", \"submit\", 3, \"disabled\"], [\"type\", \"button\", \"title\", \"Export chat history\", 1, \"export-chat-btn\", 3, \"disabled\", \"click\"], [\"type\", \"button\", \"title\", \"Export chat as file (dynamic, no storage)\", 1, \"export-chat-btn\", 3, \"disabled\", \"click\"], [\"type\", \"button\", \"title\", \"Copy entire chat to clipboard\", 1, \"copy-chat-btn\", 3, \"disabled\", \"click\"], [\"class\", \"saving-indicator\", 4, \"ngIf\"], [3, \"value\"], [1, \"debug-logs\"], [1, \"debug-header\"], [1, \"clear-logs-btn\", 3, \"click\"], [1, \"debug-content\"], [\"class\", \"debug-log-entry\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"debug-log-entry\", 3, \"ngClass\"], [1, \"log-time\"], [1, \"log-level\"], [1, \"log-message\"], [1, \"empty-state\"], [1, \"message\", 3, \"ngClass\"], [1, \"avatar\", 3, \"ngClass\"], [4, \"ngIf\"], [1, \"bubble\"], [1, \"message-header\"], [1, \"sender\"], [1, \"timestamp\"], [\"class\", \"message-type\", 4, \"ngIf\"], [\"class\", \"streaming-indicator\", 4, \"ngIf\"], [1, \"message-content\", 3, \"innerHTML\"], [\"class\", \"message-metadata\", 4, \"ngIf\"], [1, \"message-reactions\"], [1, \"reaction-buttons\"], [1, \"reaction-button\", 3, \"click\"], [1, \"message-type\"], [1, \"streaming-indicator\"], [1, \"typing-dot\"], [1, \"message-metadata\"], [1, \"execution-time\"], [1, \"typing-indicator\"], [1, \"loading-indicator\"], [1, \"spinner\"], [1, \"agent-thinking-panel\"], [1, \"thinking-header\"], [3, \"click\"], [1, \"thinking-content\"], [1, \"long-task-banner\"], [1, \"banner-text\"], [1, \"autonomous-workflow\"], [1, \"workflow-header\"], [1, \"progress-stages\"], [1, \"stage-icon\", \"planning\"], [1, \"stage-connector\"], [1, \"stage-icon\", \"design\"], [1, \"stage-icon\", \"implementation\"], [1, \"stage-icon\", \"testing\"], [1, \"progress-counter\"], [1, \"subtasks-grid\"], [\"class\", \"subtask-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"subtask-card\"], [1, \"subtask-header\"], [1, \"subtask-number\"], [1, \"subtask-type\"], [\"class\", \"model-label\", 4, \"ngIf\"], [\"class\", \"model-label local\", 4, \"ngIf\"], [\"class\", \"hot-label\", 4, \"ngIf\"], [1, \"task-description\"], [1, \"bullet\"], [1, \"output-area\"], [1, \"output-label\"], [1, \"output-content\"], [\"class\", \"web-results\", 4, \"ngIf\"], [\"class\", \"file-changes\", 4, \"ngIf\"], [\"class\", \"feedback-row\", 4, \"ngIf\"], [\"class\", \"retry-button\", 3, \"click\", 4, \"ngIf\"], [1, \"model-label\"], [1, \"model-label\", \"local\"], [1, \"hot-label\"], [1, \"web-results\"], [1, \"file-changes\"], [1, \"feedback-row\"], [1, \"feedback-options\"], [1, \"retry-button\", 3, \"click\"], [1, \"saving-indicator\"]],\n        template: function ChatComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n            i0.ɵɵtext(4, \"\\uD83E\\uDD16\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"h2\");\n            i0.ɵɵtext(6, \"Autonomous AI Agent\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"span\", 4);\n            i0.ɵɵtext(8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 5)(10, \"div\", 6)(11, \"label\", 7);\n            i0.ɵɵelement(12, \"i\", 8);\n            i0.ɵɵtext(13, \" Cloud LLM:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"select\", 9);\n            i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_Template_select_ngModelChange_14_listener($event) {\n              return ctx.selectedModel = $event;\n            })(\"change\", function ChatComponent_Template_select_change_14_listener() {\n              return ctx.onModelChange(ctx.selectedModel);\n            });\n            i0.ɵɵelementStart(15, \"optgroup\", 10);\n            i0.ɵɵtemplate(16, ChatComponent_option_16_Template, 2, 2, \"option\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"optgroup\", 12);\n            i0.ɵɵtemplate(18, ChatComponent_option_18_Template, 2, 2, \"option\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(19, ChatComponent_option_19_Template, 2, 2, \"option\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 13)(21, \"label\", 14);\n            i0.ɵɵelement(22, \"i\", 15);\n            i0.ɵɵtext(23, \" Local LLM:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"select\", 16);\n            i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_Template_select_ngModelChange_24_listener($event) {\n              return ctx.selectedLocalLlmModel = $event;\n            })(\"change\", function ChatComponent_Template_select_change_24_listener() {\n              return ctx.onLocalLlmModelChange(ctx.selectedLocalLlmModel);\n            });\n            i0.ɵɵtemplate(25, ChatComponent_option_25_Template, 2, 2, \"option\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 17)(27, \"label\", 18)(28, \"input\", 19);\n            i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_Template_input_ngModelChange_28_listener($event) {\n              return ctx.streamingEnabled = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(29, \" Streaming \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(30, \"div\", 20)(31, \"button\", 21);\n            i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_31_listener() {\n              return ctx.toggleAutonomousMode();\n            });\n            i0.ɵɵelement(32, \"i\", 22);\n            i0.ɵɵelementStart(33, \"span\");\n            i0.ɵɵtext(34);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_35_listener() {\n              return ctx.resetContextMemory();\n            });\n            i0.ɵɵelement(36, \"i\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_37_listener() {\n              return ctx.toggleApiPayloads();\n            });\n            i0.ɵɵelement(38, \"i\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"button\", 27);\n            i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_39_listener() {\n              return ctx.clearChat();\n            });\n            i0.ɵɵelement(40, \"i\", 28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"button\", 29);\n            i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_41_listener() {\n              return ctx.toggleChatExpand();\n            });\n            i0.ɵɵelement(42, \"i\", 30);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(43, ChatComponent_div_43_Template, 8, 1, \"div\", 31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"div\", 32, 33);\n            i0.ɵɵtemplate(46, ChatComponent_div_46_Template, 3, 0, \"div\", 34);\n            i0.ɵɵtemplate(47, ChatComponent_div_47_Template, 48, 65, \"div\", 35);\n            i0.ɵɵpipe(48, \"reverse\");\n            i0.ɵɵtemplate(49, ChatComponent_div_49_Template, 4, 0, \"div\", 36);\n            i0.ɵɵtemplate(50, ChatComponent_div_50_Template, 4, 0, \"div\", 37);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(51, ChatComponent_div_51_Template, 8, 1, \"div\", 38);\n            i0.ɵɵtemplate(52, ChatComponent_div_52_Template, 4, 0, \"div\", 39);\n            i0.ɵɵtemplate(53, ChatComponent_div_53_Template, 32, 17, \"div\", 40);\n            i0.ɵɵelementStart(54, \"form\", 41);\n            i0.ɵɵlistener(\"ngSubmit\", function ChatComponent_Template_form_ngSubmit_54_listener() {\n              return ctx.sendMessage();\n            });\n            i0.ɵɵelementStart(55, \"textarea\", 42);\n            i0.ɵɵlistener(\"keydown.enter\", function ChatComponent_Template_textarea_keydown_enter_55_listener($event) {\n              return ctx.onEnter($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"button\", 43)(57, \"span\");\n            i0.ɵɵtext(58, \"Send\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"button\", 44);\n            i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_59_listener() {\n              return ctx.exportChat();\n            });\n            i0.ɵɵtext(60, \"Export Chat\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"button\", 45);\n            i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_61_listener() {\n              return ctx.exportDynamicChat();\n            });\n            i0.ɵɵtext(62, \"Export Dynamic Chat\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"button\", 46);\n            i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_63_listener() {\n              return ctx.copyChat();\n            });\n            i0.ɵɵtext(64, \"Copy Chat\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(65, ChatComponent_div_65_Template, 2, 0, \"div\", 47);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(32, _c3, ctx.isChatExpanded));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(34, _c4, ctx.loading || ctx.longTaskInProgress || ctx.agentTyping));\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.loading || ctx.longTaskInProgress ? \"Working\" : ctx.agentTyping ? \"Typing...\" : \"Ready\", \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngModel\", ctx.selectedModel);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getModelsByProvider(\"openai\"));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getModelsByProvider(\"deepseek\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getOtherModels());\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngModel\", ctx.selectedLocalLlmModel);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.localLlmModels);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngModel\", ctx.streamingEnabled);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(36, _c4, ctx.autonomousMode));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.autonomousMode ? \"Autonomous\" : \"Assisted\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(38, _c4, ctx.showApiPayloads));\n            i0.ɵɵadvance(4);\n            i0.ɵɵattribute(\"aria-label\", ctx.isChatExpanded ? \"Collapse Chat\" : \"Expand Chat\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(40, _c5, ctx.isChatExpanded, !ctx.isChatExpanded));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.debugLogs.length > 0);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.messages.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(48, 30, ctx.messages));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.agentTyping);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", (ctx.loading || ctx.messagesLoading) && !ctx.agentTyping);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showAgentThinking && ctx.agentThinkingContent);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.longTaskInProgress);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.subtasks && ctx.subtasks.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.messagesLoading || ctx.messagesSaving);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.messageForm.invalid || ctx.loading || ctx.messagesLoading || ctx.messagesSaving);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.messages.length === 0 || ctx.loading || ctx.messagesSaving);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.messages.length === 0 || ctx.loading || ctx.messagesSaving);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.messages.length === 0 || ctx.loading || ctx.messagesSaving);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.messagesSaving);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i5.DatePipe, i6.ReversePipe],\n        styles: [\"@charset \\\"UTF-8\\\";.progress-stages[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin:20px 0;padding:10px 20px;background:#f0f5ff;border-radius:16px;position:relative}.progress-stages[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:8px;position:relative;z-index:2;padding:12px;border-radius:50%;background:white;box-shadow:0 2px 8px #00000014;opacity:.7;transition:all .3s ease}.progress-stages[_ngcontent-%COMP%]   .stage.active[_ngcontent-%COMP%]{opacity:1;transform:scale(1.05);box-shadow:0 4px 12px #4f8cff40}.progress-stages[_ngcontent-%COMP%]   .stage.active[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%]{background:#4f8cff;color:#fff}.progress-stages[_ngcontent-%COMP%]   .stage.active[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%]{font-weight:600;color:#2a5298}.progress-stages[_ngcontent-%COMP%]   .stage.completed[_ngcontent-%COMP%]{opacity:1}.progress-stages[_ngcontent-%COMP%]   .stage.completed[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%]{background:#22c55e;color:#fff}.progress-stages[_ngcontent-%COMP%]   .stage.completed[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%]{color:#22c55e}.progress-stages[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:#e0e0e0;display:flex;align-items:center;justify-content:center;font-size:18px;transition:all .3s ease}.progress-stages[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%]{font-size:13px;font-weight:500;color:#666;white-space:nowrap}.progress-stages[_ngcontent-%COMP%]   .stage-connector[_ngcontent-%COMP%]{flex-grow:1;height:4px;background:#e0e0e0;margin:0 -10px;position:relative;z-index:1;transition:background .3s ease}.progress-stages[_ngcontent-%COMP%]   .stage-connector.active[_ngcontent-%COMP%]{background:linear-gradient(to right,#4f8cff,#22c55e)}.progress-bar-container[_ngcontent-%COMP%]{margin:24px 0}.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%]{width:100%;height:12px;background:#f0f0f0;border-radius:6px;overflow:hidden;position:relative}.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%]   .progress-bar-fill[_ngcontent-%COMP%]{height:100%;background:linear-gradient(to right,#4f8cff,#22c55e);border-radius:6px;transition:width .5s ease;position:relative}.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%]   .progress-bar-fill.animated[_ngcontent-%COMP%]{background-size:30px 30px;background-image:linear-gradient(135deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);animation:_ngcontent-%COMP%_animate-stripes 1s linear infinite}.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%]   .progress-bar-fill[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{position:absolute;right:8px;top:50%;transform:translateY(-50%);color:#fff;font-size:10px;font-weight:600;text-shadow:0 0 2px rgba(0,0,0,.5)}.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-label[_ngcontent-%COMP%]{display:block;text-align:center;margin-top:6px;font-size:14px;color:#555}@keyframes _ngcontent-%COMP%_animate-stripes{0%{background-position:0 0}to{background-position:30px 0}}.subtasks-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px;margin-top:20px;overflow-y:auto;padding:0 20px 20px;max-height:600px}.subtask-card[_ngcontent-%COMP%]{background:white;border-radius:10px;border:1px solid #eaeaea;overflow:hidden;transition:all .3s ease;box-shadow:0 2px 8px #0000000a}.subtask-card[_ngcontent-%COMP%]:hover{box-shadow:0 4px 12px #00000014;transform:translateY(-2px)}.subtask-card.subtask-error[_ngcontent-%COMP%]{border-color:#ffbaba;background-color:#fff8f8}.subtask-card.subtask-error[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]{background:#fff0f0;border-bottom-color:#ffbaba}.subtask-card.subtask-error[_ngcontent-%COMP%]   .subtask-index[_ngcontent-%COMP%]{background:#ff5252}.subtask-card.subtask-error[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ff5252}.subtask-card.subtask-completed[_ngcontent-%COMP%]{border-color:#d0ead0}.subtask-card.subtask-completed[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]{background:#f0fff0;border-bottom-color:#d0ead0}.subtask-card.subtask-completed[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#22c55e}.subtask-card.subtask-active[_ngcontent-%COMP%]{border-color:#b3d7ff;box-shadow:0 0 0 3px #4f8cff33}.subtask-card.subtask-active[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]{background:#f0f7ff;border-bottom-color:#b3d7ff}.subtask-card.subtask-active[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#4f8cff}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:16px 20px;background:#fafbff;border-bottom:1px solid #eaeaea;position:relative}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-top-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;width:100%}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-index[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;width:28px;height:28px;background:#f0f5ff;border-radius:50%;font-size:13px;font-weight:700;box-shadow:0 2px 5px #00000014}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type[_ngcontent-%COMP%]{font-size:12px;padding:3px 8px;border-radius:6px;background:#e0e0e0;color:#333;text-transform:uppercase;letter-spacing:.5px;font-weight:600}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.file[_ngcontent-%COMP%]{background:#e3f2fd;color:#1565c0}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.command[_ngcontent-%COMP%]{background:#e8f5e9;color:#2e7d32}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.browser_test[_ngcontent-%COMP%]{background:#fffde7;color:#f57f17}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.folder[_ngcontent-%COMP%]{background:#ede7f6;color:#4527a0}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-right-controls[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]{font-size:18px}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-check-circle[_ngcontent-%COMP%]{color:#22c55e}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-exclamation-circle[_ngcontent-%COMP%]{color:#e53935}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-sync[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-circle-notch[_ngcontent-%COMP%]{color:#4f8cff}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-description[_ngcontent-%COMP%]{margin-bottom:14px;font-size:14px;color:#333;line-height:1.4;display:flex;align-items:flex-start;gap:8px}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-description[_ngcontent-%COMP%]   .fa-arrow-right[_ngcontent-%COMP%]{color:#4f8cff;margin-top:2px}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-controls[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;justify-content:space-between;flex-wrap:wrap;border-top:1px solid #f0f0f0;padding-top:10px;margin-top:6px}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .model-badge[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .web-research-badge[_ngcontent-%COMP%]{font-size:11px;padding:2px 6px;border-radius:4px;font-weight:500}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .model-badge.openai[_ngcontent-%COMP%]{background:#dcfce7;color:#166534}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .model-badge.local[_ngcontent-%COMP%]{background:#e0f2fe;color:#075985}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .web-research-badge[_ngcontent-%COMP%]{background:#ff9800;display:flex;align-items:center;gap:4px}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .web-research-badge[_ngcontent-%COMP%]:before{content:\\\"\\\\1f310\\\";font-size:12px}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{font-size:12px;padding:4px 10px;background:#f44336;color:#fff;border:none;border-radius:6px;cursor:pointer;display:flex;align-items:center;gap:5px;transition:all .2s;box-shadow:0 2px 5px #0000001a}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{background:#d32f2f;transform:translateY(-1px);box-shadow:0 3px 8px #00000026}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]{display:flex;gap:8px;margin-left:auto;align-items:center}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .feedback-label[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-right:4px}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn[_ngcontent-%COMP%]{background:white;border:1px solid #e0e0e0;cursor:pointer;color:#888;padding:6px 10px;border-radius:6px;transition:all .2s;box-shadow:0 1px 3px #0000000d;display:flex;align-items:center;justify-content:center}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn[_ngcontent-%COMP%]:hover{background:#f5f5f5;transform:translateY(-1px);box-shadow:0 2px 5px #0000001a}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn.selected[_ngcontent-%COMP%]:first-child{color:#fff;background-color:#22c55e;border-color:#22c55e}.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn.selected[_ngcontent-%COMP%]:last-child{color:#fff;background-color:#e53935;border-color:#e53935}.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]{padding:12px 16px;cursor:pointer;font-weight:500;color:#333;display:block;transition:background .2s ease}.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]:hover{background:#f9f9f9}.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]::-webkit-details-marker{display:none}.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]:before{content:\\\"\\\\25b6\\\";font-size:10px;margin-right:8px;display:inline-block;transition:transform .2s ease}details[open][_ngcontent-%COMP%]   .subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]:before{transform:rotate(90deg)}.subtask-card[_ngcontent-%COMP%]   .subtask-content[_ngcontent-%COMP%]{padding:16px;border-top:1px solid #f0f0f0}.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]{margin-bottom:12px}.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-weight:600;color:#444}.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   .copy-btn[_ngcontent-%COMP%]{background:none;border:none;color:#4f8cff;cursor:pointer;font-size:14px}.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   .copy-btn[_ngcontent-%COMP%]:hover{color:#2a5298}.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-output[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{padding:12px;font-family:monospace;font-size:13px;line-height:1.4;border-radius:6px;max-height:200px;overflow-y:auto;margin:0;white-space:pre-wrap;word-break:break-word}.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-output[_ngcontent-%COMP%]{background:#f9f9f9;color:#333;border:1px solid #eaeaea}.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{background:#fff5f5;color:#c53030;border:1px solid #feb2b2}.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .no-output[_ngcontent-%COMP%]{padding:12px;color:#666;font-style:italic;background:#f9f9f9;border-radius:6px;text-align:center}.subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%]{cursor:pointer;padding:8px 12px;background:#f9f9f9;border-radius:6px;margin-bottom:8px;font-size:13px;font-weight:500;color:#444;display:flex;align-items:center;gap:8px}.subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%]::-webkit-details-marker, .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%]::-webkit-details-marker{display:none}.subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   .web-results-content[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   .web-results-content[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%]{background:#f5f5f5;padding:12px;border-radius:6px;font-family:monospace;font-size:12px;line-height:1.4;max-height:150px;overflow-y:auto;white-space:pre-wrap;word-break:break-word}.subtask-card[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%]   .diff-added[_ngcontent-%COMP%]{color:#22c55e;background-color:#f0fff4;display:block}.subtask-card[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%]   .diff-removed[_ngcontent-%COMP%]{color:#e53e3e;background-color:#fff5f5;display:block}.subtask-card[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%]   .diff-hunk[_ngcontent-%COMP%]{color:#805ad5;background-color:#f8f0fc;display:block;padding:2px 0;margin:8px 0 4px}.feedback-thanks[_ngcontent-%COMP%]{margin-left:auto;font-size:12px;color:#22c55e;display:flex;align-items:center;gap:5px;padding:4px 10px;border-radius:4px;background-color:#22c55e1a}.feedback-btns[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-left:auto}.feedback-label[_ngcontent-%COMP%]{font-size:13px;color:#555;margin-right:5px}.thumb-btn[_ngcontent-%COMP%]{background:#f9f9f9;border:1px solid #e0e0e0;padding:6px 10px;border-radius:6px;transition:all .2s;display:flex;align-items:center;justify-content:center;box-shadow:0 1px 3px #0000000d}.thumb-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.thumb-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 5px #0000001a}.thumb-btn.selected[_ngcontent-%COMP%]:first-child{color:#fff;background-color:#22c55e;border-color:#22c55e}.thumb-btn.selected[_ngcontent-%COMP%]:last-child{color:#fff;background-color:#e53935;border-color:#e53935}.autonomous-workflow[_ngcontent-%COMP%]{margin-top:20px;padding:15px;background:#f9fafc;border-radius:8px;box-shadow:0 1px 3px #0000000d}.workflow-header[_ngcontent-%COMP%]{margin-bottom:15px;display:flex;align-items:center}.workflow-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;font-size:16px;font-weight:500;color:#333}.progress-stages[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin-bottom:15px}.progress-stages[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;opacity:.5}.progress-stages[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:32px;height:32px;display:flex;align-items:center;justify-content:center;background:#e8f0fe;border-radius:50%;margin-bottom:5px;font-size:16px}.progress-stages[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{font-size:12px;color:#666}.progress-stages[_ngcontent-%COMP%]   .stage-icon.active[_ngcontent-%COMP%]{opacity:1}.progress-stages[_ngcontent-%COMP%]   .stage-icon.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:#4285f4;color:#fff}.progress-stages[_ngcontent-%COMP%]   .stage-icon.active[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{color:#333;font-weight:500}.progress-stages[_ngcontent-%COMP%]   .stage-connector[_ngcontent-%COMP%]{flex-grow:1;height:2px;background:#e0e0e0;margin:0 5px}.progress-stages[_ngcontent-%COMP%]   .stage-connector.active[_ngcontent-%COMP%]{background:#4285f4}.progress-counter[_ngcontent-%COMP%]{text-align:right;font-size:13px;color:#666;margin-bottom:15px}.subtasks-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(240px,1fr));gap:15px;padding-bottom:10px}.subtask-card[_ngcontent-%COMP%]{background:white;border-radius:8px;box-shadow:0 1px 3px #0000001a;overflow:hidden;border:1px solid #eaeaea;position:relative;display:flex;flex-direction:column}.subtask-card[_ngcontent-%COMP%]:hover{box-shadow:0 2px 5px #00000026}.subtask-card.running[_ngcontent-%COMP%]{border-color:#4285f4;box-shadow:0 0 0 1px #4285f433}.subtask-card.completed[_ngcontent-%COMP%]{border-color:#34a853;box-shadow:0 0 0 1px #34a85333}.subtask-card.error[_ngcontent-%COMP%]{border-color:#ea4335;box-shadow:0 0 0 1px #ea433533}.subtask-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:8px 10px;background:#f4f8fc;border-bottom:1px solid #e1e7ed;height:40px}.subtask-header[_ngcontent-%COMP%]   .subtask-number[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:24px;height:24px;border-radius:50%;background:#4285f4;color:#fff;font-size:12px;font-weight:500;margin-right:10px}.subtask-header[_ngcontent-%COMP%]   .subtask-type[_ngcontent-%COMP%]{text-transform:uppercase;font-size:11px;background:#e8f0fe;color:#1967d2;padding:3px 8px;border-radius:3px;letter-spacing:.5px;font-weight:500;height:20px;display:flex;align-items:center;justify-content:center}.subtask-header[_ngcontent-%COMP%]   .model-label[_ngcontent-%COMP%]{margin-left:auto;font-size:11px;padding:2px 6px;border-radius:3px;background:#1a73e8;color:#fff;font-weight:500;height:20px;display:flex;align-items:center;justify-content:center}.subtask-header[_ngcontent-%COMP%]   .model-label.local[_ngcontent-%COMP%]{background:#9334e6}.subtask-header[_ngcontent-%COMP%]   .hot-label[_ngcontent-%COMP%]{font-size:11px;padding:2px 6px;border-radius:3px;background:#ea4335;color:#fff;font-weight:500;margin-left:5px;height:20px;display:flex;align-items:center;justify-content:center}.task-description[_ngcontent-%COMP%]{padding:10px;display:flex;align-items:flex-start;gap:10px;border-bottom:1px solid #e8f0fe;min-height:40px}.task-description[_ngcontent-%COMP%]   .bullet[_ngcontent-%COMP%]{color:#4285f4;font-size:11px;margin-top:2px;width:12px;display:flex;justify-content:center}.task-description[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:last-child{font-size:13px;line-height:1.5;color:#202124;font-weight:400;flex:1}.output-area[_ngcontent-%COMP%]{padding:10px;flex-grow:1;display:flex;flex-direction:column}.output-area[_ngcontent-%COMP%]   .output-label[_ngcontent-%COMP%]{font-weight:500;font-size:13px;margin-bottom:6px;color:#202124}.output-area[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]{padding:8px 10px;background:#f8f9fa;border-radius:4px;font-family:Roboto Mono,monospace;font-size:12px;line-height:1.5;color:#3c4043;max-height:120px;min-height:30px;overflow-y:auto;white-space:pre-wrap;word-break:break-word;border:1px solid #e8eaed;margin-bottom:8px}.output-area[_ngcontent-%COMP%]   .output-content.error[_ngcontent-%COMP%]{background:#fce8e6;color:#c5221f;border-color:#f6bbb8}.output-area[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%], .output-area[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]{font-size:12px;color:#1a73e8;margin-top:0;cursor:pointer;display:flex;align-items:center;height:24px}.output-area[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]:before, .output-area[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]:before{content:\\\"\\\";display:inline-block;width:6px;height:6px;background:#1a73e8;border-radius:50%;margin-right:5px}.output-area[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]:hover, .output-area[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]:hover{text-decoration:underline}.feedback-row[_ngcontent-%COMP%]{padding:10px;display:flex;align-items:center;justify-content:space-between;border-top:1px solid #e8f0fe;background:#f8f9fa;height:36px}.feedback-row[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;color:#5f6368;margin-left:3px}.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]{display:flex;gap:10px}.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:16px;height:16px;border-radius:3px;border:1px solid #e0e0e0;background:white;cursor:pointer;position:relative;padding:0}.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child:after{content:\\\"\\\";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:8px;height:8px;border-radius:1px;background:#4caf50;opacity:0}.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child.selected:after{opacity:1}.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child:after{content:\\\"\\\";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:8px;height:8px;border-radius:1px;background:#f44336;opacity:0}.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child.selected:after{opacity:1}.retry-button[_ngcontent-%COMP%]{display:block;margin:10px 10px 10px auto;padding:4px 10px;background:#ea4335;color:#fff;border:none;border-radius:3px;font-size:12px;font-weight:500;cursor:pointer;box-shadow:0 1px 2px #3c40434d}.retry-button[_ngcontent-%COMP%]:hover{background:#d32f2f;box-shadow:0 2px 4px #3c40434d}.browser-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e3f2fd,#bbdefb)!important;border:1px solid #90caf9!important;border-radius:12px!important;color:#0d47a1!important}.browser-avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#2196f3,#64b5f6)!important;color:#fff!important}.openai-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f3e5f5,#e1bee7)!important;border:1px solid #ce93d8!important;border-radius:12px!important;color:#4a148c!important}.openai-avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#9c27b0,#ba68c8)!important;color:#fff!important}.llm-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e8f5e9,#c8e6c9)!important;border:1px solid #a5d6a7!important;border-radius:12px!important;color:#1b5e20!important}.llm-avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4caf50,#81c784)!important;color:#fff!important}.error-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffebee,#ffcdd2)!important;border:1px solid #ef9a9a!important;border-radius:12px!important;color:#b71c1c!important}.error-avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f44336,#e57373)!important;color:#fff!important}.system-notification[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff8e1,#ffe0b2)!important;border:1px solid #ffcc80!important;border-radius:12px!important;color:#e65100!important}.message-metadata[_ngcontent-%COMP%]{margin-top:8px;font-size:12px;color:#0009;display:flex;flex-wrap:wrap;gap:8px}.execution-time[_ngcontent-%COMP%]{padding:2px 6px;background-color:#0000000d;border-radius:4px;display:inline-flex;align-items:center}.message-type[_ngcontent-%COMP%]{font-size:12px;padding:2px 6px;background-color:#0000000d;border-radius:4px;margin-left:8px}.user-message[_ngcontent-%COMP%]   .message-metadata[_ngcontent-%COMP%]{color:#fffc}.user-message[_ngcontent-%COMP%]   .execution-time[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-type[_ngcontent-%COMP%]{background-color:#fff3}.chat-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;background:linear-gradient(135deg,#fafdff 80%,#e3f0ff 100%);border-radius:18px;box-shadow:0 6px 32px #0000001a;overflow:hidden;margin:0 auto;transition:box-shadow .3s}.chat-container[_ngcontent-%COMP%]:focus-within, .chat-container[_ngcontent-%COMP%]:hover{box-shadow:0 12px 48px #0000002e}.chat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:5px 16px;background:#3378d1;color:#fff;border-top-left-radius:8px;border-top-right-radius:8px;height:46px;box-shadow:0 2px 12px #0000000f;animation:_ngcontent-%COMP%_fadeInDown .5s}@keyframes _ngcontent-%COMP%_fadeInDown{0%{opacity:0;transform:translateY(-20px)}to{opacity:1;transform:translateY(0)}}.agent-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.agent-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:22px;font-weight:700;letter-spacing:.5px;white-space:nowrap}.agent-title[_ngcontent-%COMP%]   .agent-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:4px}.agent-title[_ngcontent-%COMP%]   .agent-status[_ngcontent-%COMP%]{font-size:12px;padding:4px 8px;border-radius:12px;background-color:#fff3;color:#fff;text-transform:uppercase;font-weight:600;letter-spacing:.5px}.agent-title[_ngcontent-%COMP%]   .agent-status.active[_ngcontent-%COMP%]{background-color:#22c55e;animation:_ngcontent-%COMP%_pulse 1.5s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #22c55e66}70%{box-shadow:0 0 0 8px #22c55e00}to{box-shadow:0 0 #22c55e00}}.model-controls[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:center;flex-wrap:wrap}.model-selector[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;background:rgba(255,255,255,.18);padding:7px 14px;border-radius:8px;box-shadow:0 1px 2px #0000000a;transition:background .2s}.model-selector[_ngcontent-%COMP%]   .autonomous-workflow[_ngcontent-%COMP%]{margin-top:16px;padding:20px;background:linear-gradient(to bottom,#f5f7fa,#ffffff);border-radius:12px;box-shadow:0 4px 20px #0000000f;animation:_ngcontent-%COMP%_fadeIn .5s ease-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin-bottom:16px}.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#2a5298;margin:0;display:flex;align-items:center;gap:8px}.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px;color:#4f8cff}.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   .workflow-controls[_ngcontent-%COMP%]{display:flex;gap:8px}.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   .workflow-action-btn[_ngcontent-%COMP%]{background-color:#f0f5ff;border:1px solid #d0e1ff;border-radius:6px;padding:6px 10px;color:#4f8cff;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;justify-content:center}.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   .workflow-action-btn[_ngcontent-%COMP%]:hover{background-color:#e0edff;transform:translateY(-1px)}.model-selector[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{margin:0;font-size:15px;font-weight:500;color:#2d3a4a}.model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{padding:6px 12px 6px 32px;border-radius:6px;border:1.5px solid #b3d7ff;background:url('data:image/svg+xml;utf8,<svg fill=\\\"%234f8cff\\\" height=\\\"16\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"16\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M7 10l5 5 5-5z\\\"/></svg>') no-repeat 8px center/18px 18px,#fff;color:#333;font-size:15px;transition:border .2s;appearance:none}.model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus{border:1.5px solid #4f8cff;outline:none}.clear-chat-btn[_ngcontent-%COMP%]{background:linear-gradient(90deg,#e57373 60%,#ffb199 100%);border:none;color:#fff;padding:7px 18px;border-radius:8px;cursor:pointer;font-size:15px;font-weight:600;transition:background .2s,color .2s,box-shadow .2s;margin-left:10px;box-shadow:0 1px 4px #0000000f;display:flex;align-items:center;gap:6px}.clear-chat-btn[_ngcontent-%COMP%]:before{content:\\\"\\\\1f5d1\\\";font-size:1.1em}.clear-chat-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#ffb199 60%,#e57373 100%);color:#fffde7;box-shadow:0 2px 8px #e573732e}.mode-toggle-btn[_ngcontent-%COMP%]{padding:6px 12px;border-radius:16px;background-color:#ffffff26;border:1px solid rgba(255,255,255,.3);color:#fff;font-size:13px;font-weight:600;transition:all .2s ease;min-width:120px;text-align:center;white-space:nowrap;cursor:pointer;display:flex;align-items:center;justify-content:center;gap:6px;box-shadow:0 1px 3px #0000001a}.mode-toggle-btn[_ngcontent-%COMP%]   .fa-robot[_ngcontent-%COMP%]{font-size:14px}.mode-toggle-btn[_ngcontent-%COMP%]:hover{background-color:#ffffff40;transform:translateY(-1px);box-shadow:0 2px 5px #00000026}.mode-toggle-btn.active[_ngcontent-%COMP%]{background-color:#22c55e;border-color:#16a34a}.clear-chat-btn[_ngcontent-%COMP%]{background-color:#ffffff26;color:#fff;border:1px solid rgba(255,255,255,.3);padding:6px 10px;border-radius:6px;font-size:14px;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;justify-content:center;box-shadow:0 1px 3px #0000001a;margin:0 5px}.clear-chat-btn[_ngcontent-%COMP%]:hover{background-color:#ff3c3ccc;transform:translateY(-1px);box-shadow:0 2px 5px #00000026}.expand-chat-btn[_ngcontent-%COMP%]{background:rgba(255,255,255,.15);border:1px solid rgba(255,255,255,.3);color:#fff;font-size:14px;cursor:pointer;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:50%;transition:all .2s ease;margin-left:5px;box-shadow:0 1px 3px #0000001a}.expand-chat-btn[_ngcontent-%COMP%]:hover{background-color:#ffffff40;transform:translateY(-1px);box-shadow:0 2px 5px #00000026}.messages-container[_ngcontent-%COMP%]{flex:1 1 auto;padding:0 32px;overflow-y:auto;display:flex;flex-direction:column-reverse;gap:18px;background:transparent;scrollbar-width:thin;scrollbar-color:#b3d7ff #fafdff;animation:_ngcontent-%COMP%_fadeIn .6s;max-height:300px;min-height:80px}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:8px}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#b3d7ff;border-radius:4px}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#fafdff}.message[_ngcontent-%COMP%]{display:flex;align-items:flex-end;gap:12px;opacity:0;animation:_ngcontent-%COMP%_slideIn .4s forwards}@keyframes _ngcontent-%COMP%_slideIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.avatar[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:50%;background:linear-gradient(135deg,#4f8cff 60%,#a0c4ff 100%);color:#fff;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:18px;box-shadow:0 1px 4px #00000014;flex-shrink:0;margin-bottom:2px}.user-message[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffb199 60%,#e57373 100%)}.system-message[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffc107 60%,#ff9800 100%)}.bubble[_ngcontent-%COMP%]{padding:14px 20px;border-radius:16px;max-width:420px;min-width:60px;word-break:break-word;box-shadow:0 2px 12px #0000000f;font-size:16px;line-height:1.6;position:relative;transition:background .2s}.user-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ffb199 60%,#e57373 100%);color:#fff;border-bottom-right-radius:4px;align-self:flex-end}.agent-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{background:#fafdff;color:#2d3a4a;border-bottom-left-radius:4px;align-self:flex-start}.message-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:4px;font-size:13px;opacity:.8}.sender[_ngcontent-%COMP%]{font-weight:700}.timestamp[_ngcontent-%COMP%]{opacity:.7}.message-content[_ngcontent-%COMP%]{line-height:1.5;font-size:15px}.message-form[_ngcontent-%COMP%]{display:flex;gap:14px;padding:12px 32px 16px;background:#fff;border-top:1px solid #e0e0e0;border-bottom-left-radius:8px;border-bottom-right-radius:8px;box-shadow:0 -1px 4px #00000008;align-items:flex-end}.message-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{flex:1;padding:10px;border:1px solid #ddd;border-radius:4px;resize:none;font-family:inherit;height:40px;font-size:14px;background:white}.message-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{border:1.5px solid #4f8cff;outline:none}.message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:8px 16px;background:#e0e0e0;color:#555;border:none;border-radius:4px;cursor:pointer;font-size:14px;font-weight:500;transition:background .2s;display:flex;align-items:center}.message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{background:#ccc;cursor:not-allowed}.message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:not(:disabled):hover{background:#d0d0d0}.empty-state[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:180px;color:#888;text-align:center;font-size:17px;opacity:.8}.loading-indicator[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:10px;padding:20px;color:#777}.spinner[_ngcontent-%COMP%]{border:3px solid rgba(0,0,0,.1);border-top:3px solid #4f8cff;border-radius:50%;width:28px;height:28px;animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 900px){.chat-header[_ngcontent-%COMP%], .message-form[_ngcontent-%COMP%], .messages-container[_ngcontent-%COMP%]{padding-left:12px;padding-right:12px}.messages-container[_ngcontent-%COMP%]{max-height:180px;min-height:60px}}@media (max-width: 600px){.chat-header[_ngcontent-%COMP%], .message-form[_ngcontent-%COMP%], .messages-container[_ngcontent-%COMP%]{padding-left:2px;padding-right:2px}.chat-header[_ngcontent-%COMP%]{font-size:18px;padding-top:10px;padding-bottom:10px}.message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:10px 16px;font-size:15px}}.chat-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;background:#ffffff;border-radius:8px;box-shadow:0 3px 10px #0000001a;margin:0 auto;transition:all .3s ease;position:relative;min-height:46px;overflow:visible}.message-form[_ngcontent-%COMP%]{display:flex!important;gap:10px;padding:10px;background:white;position:sticky;bottom:0;z-index:1000;align-items:center;width:100%;flex-wrap:wrap;border-top:1px solid #e0e0e0}.chat-container[_ngcontent-%COMP%]:not(.expanded)   .messages-container[_ngcontent-%COMP%], .chat-container[_ngcontent-%COMP%]:not(.expanded)   .autonomous-workflow[_ngcontent-%COMP%]{display:none}.file-changes-container[_ngcontent-%COMP%]{display:none!important}.chat-container.expanded[_ngcontent-%COMP%]{height:auto;max-height:600px}.messages-container[_ngcontent-%COMP%]{flex:1;max-height:150px;overflow-y:auto;transition:all .3s}.chat-container.expanded[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]{max-height:400px}.chat-container.expanded[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]{border-radius:10px 10px 0 0;position:sticky;top:0;z-index:10}.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;flex-wrap:wrap;flex:1;margin:0 20px}.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background:rgba(255,255,255,.15);padding:4px 12px;border-radius:16px;border:1px solid rgba(255,255,255,.3);margin-right:10px}.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:13px;font-weight:600;white-space:nowrap}.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{background:rgba(255,255,255,.2);border:none;color:#fff;padding:4px 8px;border-radius:4px;font-size:13px;min-width:140px}.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%]{background:#fff;color:#333}.chat-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.expand-chat-btn[_ngcontent-%COMP%]{background:linear-gradient(90deg,#4f8cff 80%,#a0c4ff 100%);color:#fff;opacity:1!important;visibility:visible!important}.file-changes-wrapper[_ngcontent-%COMP%]{margin:10px 0;border:1px solid #e0e0e0;border-radius:4px;overflow:hidden;background:#f8f9fa;width:100%}.file-changes[_ngcontent-%COMP%]{background:#f8f9fa;width:100%}.file-changes-header[_ngcontent-%COMP%]{padding:8px 12px;background:#f1f3f4;font-weight:500;display:flex;align-items:center;gap:8px;cursor:pointer;border-bottom:1px solid #e0e0e0}.file-diff[_ngcontent-%COMP%]{padding:10px;font-family:Roboto Mono,monospace;font-size:12px;line-height:1.5;white-space:pre-wrap;overflow-x:auto;max-height:300px;overflow-y:auto;background:#f8f9fa;color:#333;border:1px solid #eee;margin:0}.diff-added[_ngcontent-%COMP%]{color:#28a745;background-color:#e6ffec;display:block}.diff-removed[_ngcontent-%COMP%]{color:#d73a49;background-color:#ffeef0;display:block}.diff-hunk[_ngcontent-%COMP%]{color:#0366d6;background-color:#f1f8ff;display:block;font-weight:700}.chat-container.expanded[_ngcontent-%COMP%]{max-height:none;height:auto;position:relative}.chat-container.expanded[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%], .chat-container.expanded[_ngcontent-%COMP%]   .autonomous-workflow[_ngcontent-%COMP%]{max-height:400px;overflow-y:auto}.chat-container[_ngcontent-%COMP%]{position:relative!important;z-index:1;transition:all .3s ease;box-shadow:0 3px 10px #0000001a;min-height:150px;display:flex;flex-direction:column;justify-content:space-between}.expand-chat-btn[_ngcontent-%COMP%]{background:linear-gradient(90deg,#4f8cff 80%,#a0c4ff 100%);color:#fff;border:none;border-radius:8px;font-size:18px;font-weight:600;cursor:pointer;box-shadow:0 1px 4px #0000000f;margin-left:10px;padding:6px 14px;transition:background .2s,box-shadow .2s;display:flex;align-items:center}.expand-chat-btn[_ngcontent-%COMP%]:hover{background:#4f8cff;box-shadow:0 2px 8px #4f8cff2e}.export-chat-btn[_ngcontent-%COMP%], .copy-chat-btn[_ngcontent-%COMP%]{margin-left:8px;padding:6px 14px;background:#1976d2;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:14px;transition:background .2s}.export-chat-btn[_ngcontent-%COMP%]:disabled, .copy-chat-btn[_ngcontent-%COMP%]:disabled{background:#bdbdbd;cursor:not-allowed}.export-chat-btn[_ngcontent-%COMP%]:hover:not(:disabled), .copy-chat-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#1565c0}.typing-indicator[_ngcontent-%COMP%]{display:flex;padding:10px 20px;align-items:center;gap:4px}.typing-dot[_ngcontent-%COMP%]{width:8px;height:8px;background-color:#4f8cff;border-radius:50%;animation:_ngcontent-%COMP%_typing-animation 1.4s infinite ease-in-out;opacity:.7}.typing-dot[_ngcontent-%COMP%]:nth-child(1){animation-delay:0s}.typing-dot[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.typing-dot[_ngcontent-%COMP%]:nth-child(3){animation-delay:.4s}@keyframes _ngcontent-%COMP%_typing-animation{0%,to{transform:scale(.7);opacity:.5}50%{transform:scale(1.2);opacity:1}}.message.streaming[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{border-right:2px solid #4f8cff;animation:_ngcontent-%COMP%_cursor-blink 1s infinite}@keyframes _ngcontent-%COMP%_cursor-blink{0%,to{border-color:transparent}50%{border-color:#4f8cff}}.message-reactions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding:5px 0}.reaction-buttons[_ngcontent-%COMP%]{display:flex;gap:6px}.reaction-button[_ngcontent-%COMP%]{background:none;border:1px solid #e0e0e0;border-radius:16px;padding:4px 8px;font-size:14px;cursor:pointer;transition:all .2s}.reaction-button[_ngcontent-%COMP%]:hover{background-color:#f5f5f5;transform:translateY(-1px)}.reaction-button.active[_ngcontent-%COMP%]{background-color:#e3f2fd;border-color:#bbdefb;color:#1976d2}.agent-thinking-panel[_ngcontent-%COMP%]{margin:10px;border:1px solid #e0e0e0;border-radius:8px;overflow:hidden;box-shadow:0 2px 8px #0000001a}.thinking-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:10px 15px;background-color:#f5f5f5;border-bottom:1px solid #e0e0e0}.thinking-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;font-size:16px;font-weight:600;color:#333}.thinking-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:none;border:none;color:#666;cursor:pointer;font-size:14px;padding:4px 8px;border-radius:4px}.thinking-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:#e0e0e0}.thinking-content[_ngcontent-%COMP%]{padding:10px 15px;background-color:#fafafa;font-family:monospace;font-size:14px;line-height:1.5;overflow-x:auto;margin:0;white-space:pre-wrap}.memory-btn[_ngcontent-%COMP%]{background:linear-gradient(90deg,#4f8cff 0%,#2979ff 100%);border:none;color:#fff;padding:7px 12px;border-radius:8px;cursor:pointer;font-size:15px;font-weight:600;transition:all .2s;box-shadow:0 1px 4px #0000000f;display:flex;align-items:center;justify-content:center}.memory-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#2979ff 0%,#1565c0 100%);transform:translateY(-1px);box-shadow:0 2px 8px #2979ff33}.memory-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.streaming-toggle[_ngcontent-%COMP%]{background:rgba(255,255,255,.18);padding:5px 10px;border-radius:8px;color:#fff;display:flex;align-items:center}.streaming-toggle[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;cursor:pointer;font-size:14px;font-weight:500;margin:0}.streaming-toggle[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{accent-color:#22c55e;width:16px;height:16px}.saving-indicator[_ngcontent-%COMP%]{position:absolute;bottom:-20px;left:10px;font-size:12px;color:#888;background-color:#ffffffb3;padding:2px 6px;border-radius:4px;animation:_ngcontent-%COMP%_pulse 1.5s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:.6}50%{opacity:1}to{opacity:.6}}.api-toggle-btn[_ngcontent-%COMP%]{background-color:#4a4a4a;color:#fff;border:none;border-radius:4px;padding:8px;margin-right:5px;cursor:pointer;transition:background-color .2s}.api-toggle-btn[_ngcontent-%COMP%]:hover{background-color:#666}.api-toggle-btn.active[_ngcontent-%COMP%]{background-color:#3498db}.message.api-request-message[_ngcontent-%COMP%], .message.api-response-message[_ngcontent-%COMP%]{background-color:#f8f9fa;border-left:3px solid #3498db}.message.api-request-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%], .message.api-response-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{background-color:#f8f9fa;border:1px solid #e0e0e0}.message.api-request-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%], .message.api-response-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background-color:#f1f1f1;padding:10px;border-radius:4px;overflow-x:auto;font-family:Consolas,Monaco,monospace;font-size:12px;line-height:1.4;max-height:300px;overflow-y:auto}.message.api-request-message[_ngcontent-%COMP%]{border-left-color:#3498db}.message.api-request-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{border-left:3px solid #3498db}.message.api-response-message[_ngcontent-%COMP%]{border-left-color:#2ecc71}.message.api-response-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{border-left:3px solid #2ecc71}.api-request-avatar[_ngcontent-%COMP%], .api-response-avatar[_ngcontent-%COMP%]{background-color:#f8f9fa;color:#333}.api-request-avatar[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .api-response-avatar[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px}.api-request-avatar[_ngcontent-%COMP%]{background-color:#d4e6f1}.api-response-avatar[_ngcontent-%COMP%]{background-color:#d5f5e3}.debug-logs[_ngcontent-%COMP%]{background:#f8f9fa;border:1px solid #dee2e6;border-radius:4px;margin:10px 0;max-height:200px;overflow-y:auto}.debug-logs[_ngcontent-%COMP%]   .debug-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px 12px;background:#e9ecef;border-bottom:1px solid #dee2e6;font-weight:700;font-size:12px}.debug-logs[_ngcontent-%COMP%]   .debug-header[_ngcontent-%COMP%]   .clear-logs-btn[_ngcontent-%COMP%]{background:#dc3545;color:#fff;border:none;padding:4px 8px;border-radius:3px;font-size:10px;cursor:pointer}.debug-logs[_ngcontent-%COMP%]   .debug-header[_ngcontent-%COMP%]   .clear-logs-btn[_ngcontent-%COMP%]:hover{background:#c82333}.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]{padding:8px}.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry[_ngcontent-%COMP%]{display:flex;gap:8px;padding:2px 0;font-family:Courier New,monospace;font-size:11px}.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry[_ngcontent-%COMP%]   .log-time[_ngcontent-%COMP%]{color:#6c757d;min-width:60px}.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry[_ngcontent-%COMP%]   .log-level[_ngcontent-%COMP%]{min-width:50px;font-weight:700}.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry.log-info[_ngcontent-%COMP%]   .log-level[_ngcontent-%COMP%]{color:#0d6efd}.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry.log-error[_ngcontent-%COMP%]   .log-level[_ngcontent-%COMP%]{color:#dc3545}.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry.log-warning[_ngcontent-%COMP%]   .log-level[_ngcontent-%COMP%]{color:#fd7e14}.debug-logs[_ngcontent-%COMP%]   .debug-content[_ngcontent-%COMP%]   .debug-log-entry[_ngcontent-%COMP%]   .log-message[_ngcontent-%COMP%]{flex:1;color:#212529}\"]\n      });\n    }\n  }\n  return ChatComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}