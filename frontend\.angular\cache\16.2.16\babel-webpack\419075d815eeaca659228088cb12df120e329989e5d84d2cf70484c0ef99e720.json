{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { HomeComponent } from './components/home/<USER>';\nimport { ProjectListComponent } from './components/project-list/project-list.component';\nimport { ProjectDetailComponent } from './components/project-detail/project-detail.component';\nimport { ConfigComponent } from './components/config/config.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HomeComponent\n}, {\n  path: 'projects',\n  component: ProjectListComponent\n}, {\n  path: 'projects/:name',\n  component: ProjectDetailComponent\n}, {\n  path: 'config',\n  component: ConfigComponent\n}, {\n  path: '**',\n  redirectTo: ''\n}];\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static {\n      this.ɵfac = function AppRoutingModule_Factory(t) {\n        return new (t || AppRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forRoot(routes), RouterModule]\n      });\n    }\n  }\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}